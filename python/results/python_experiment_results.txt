Python KNN Experiment Results
========================================

Configuration:
  k = 5
  distance_metric = euclidean
  weights = uniform

Manual Implementation:
  Accuracy: 91.11%
  Execution time: 0.0439s

Scikit-Learn Implementation:
  Accuracy: 91.11%
  Execution time: 0.0100s

Classification Report (Manual):
              precision    recall  f1-score   support

      Setosa       1.00      1.00      1.00        15
  Versicolor       0.79      1.00      0.88        15
   Virginica       1.00      0.73      0.85        15

    accuracy                           0.91        45
   macro avg       0.93      0.91      0.91        45
weighted avg       0.93      0.91      0.91        45


Confusion Matrix (Manual):
[[15  0  0]
 [ 0 15  0]
 [ 0  4 11]]