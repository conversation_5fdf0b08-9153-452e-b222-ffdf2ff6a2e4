"""
Final summary plots highlighting key differences between Idris2 and Python KNN implementations.
HACK: some of the plot styling is a bit messy but it works
"""

import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path

# Set style for better plots
try:
    plt.style.use('seaborn-v0_8')
except OSError:
    plt.style.use('seaborn')

def create_final_summary():
    """Create final summary visualization."""
    
    # Data from our analysis
    datasets = ['Iris', 'Wine', 'Synthetic']
    test_samples = [45, 36, 600]
    
    # Accuracy data
    idris_acc = [91.1, 86.1, 74.5]
    python_acc = [91.1, 97.2, 82.4]  # Average of manual and sklearn
    
    # Agreement data
    agreement = [100.0, 83.3, 78.3]  # Average agreement with Python implementations
    
    # Create figure
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
    
    # Plot 1: Accuracy Comparison
    x = np.arange(len(datasets))
    width = 0.35
    
    bars1 = ax1.bar(x - width/2, idris_acc, width, label='Idris2', color='#FF6B6B', alpha=0.8)
    bars2 = ax1.bar(x + width/2, python_acc, width, label='Python (Avg)', color='#4ECDC4', alpha=0.8)
    
    ax1.set_xlabel('Dataset', fontweight='bold', fontsize=12)
    ax1.set_ylabel('Accuracy (%)', fontweight='bold', fontsize=12)
    ax1.set_title('Idris2 vs Python KNN Accuracy', fontweight='bold', fontsize=14)
    ax1.set_xticks(x)
    ax1.set_xticklabels(datasets)
    ax1.legend(fontsize=11)
    ax1.grid(True, alpha=0.3)
    ax1.set_ylim(0, 105)
    
    # Add value labels
    for bars in [bars1, bars2]:
        for bar in bars:
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height + 1,
                    f'{height:.1f}%', ha='center', va='bottom', fontweight='bold')
    
    # Plot 2: Performance Gap
    performance_gap = [p - i for i, p in zip(idris_acc, python_acc)]
    colors = ['green' if gap >= 0 else 'red' for gap in performance_gap]
    
    bars = ax2.bar(datasets, performance_gap, color=colors, alpha=0.7)
    ax2.set_xlabel('Dataset', fontweight='bold', fontsize=12)
    ax2.set_ylabel('Python Advantage (%)', fontweight='bold', fontsize=12)
    ax2.set_title('Performance Gap: Python vs Idris2', fontweight='bold', fontsize=14)
    ax2.grid(True, alpha=0.3)
    ax2.axhline(y=0, color='black', linestyle='-', alpha=0.5)
    
    # Add value labels
    for bar, gap in zip(bars, performance_gap):
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + (0.3 if height >= 0 else -0.8),
                f'{gap:+.1f}%', ha='center', va='bottom' if height >= 0 else 'top', 
                fontweight='bold', fontsize=11)
    
    # Plot 3: Dataset Size vs Agreement
    ax3.scatter(test_samples, agreement, s=200, alpha=0.7, color='#FF6B6B')
    ax3.set_xlabel('Test Samples (Dataset Size)', fontweight='bold', fontsize=12)
    ax3.set_ylabel('Idris-Python Agreement (%)', fontweight='bold', fontsize=12)
    ax3.set_title('Dataset Size vs Implementation Agreement', fontweight='bold', fontsize=14)
    ax3.grid(True, alpha=0.3)
    ax3.set_ylim(75, 105)
    
    # Add dataset labels
    for i, (size, agr) in enumerate(zip(test_samples, agreement)):
        ax3.annotate(datasets[i], (size, agr), xytext=(10, 10), 
                    textcoords='offset points', fontsize=11, fontweight='bold')
    
    # Add trend line
    z = np.polyfit(test_samples, agreement, 1)
    p = np.poly1d(z)
    ax3.plot(test_samples, p(test_samples), "r--", alpha=0.8, linewidth=2)
    
    # Plot 4: Key Insights Summary
    ax4.axis('off')
    
    insights = [
        "KEY INSIGHTS",
        "",
        "SMALL DATASETS (Iris):",
        "   • Perfect agreement (100%)",
        "   • Identical accuracy (91.1%)",
        "   • No meaningful differences",
        "",
        "Warning: MEDIUM DATASETS (Wine):",
        "   • Significant accuracy gap (11.1%)",
        "   • Reduced agreement (83.3%)",
        "   • Algorithmic differences emerge",
        "",
        "LARGE DATASETS (Synthetic):",
        "   • Major performance gap (7.9%)",
        "   • Poor agreement (78.3%)",
        "   • Clear implementation differences",
        "",
        "SCIENTIFIC CONCLUSION:",
        "   Dataset size reveals implementation",
        "   differences masked in small datasets",
        "",
        "TYPE SAFETY vs PERFORMANCE:",
        "   Idris2 provides compile-time safety",
        "   but at the cost of runtime accuracy"
    ]
    
    y_pos = 0.95
    for insight in insights:
        if insight.startswith(""):
            ax4.text(0.5, y_pos, insight, ha='center', va='top', fontsize=16, 
                    fontweight='bold', transform=ax4.transAxes)
        elif insight.startswith(("", "Warning: ", "", "", "")):
            ax4.text(0.05, y_pos, insight, ha='left', va='top', fontsize=12, 
                    fontweight='bold', transform=ax4.transAxes)
        elif insight.startswith("   •"):
            ax4.text(0.1, y_pos, insight, ha='left', va='top', fontsize=11, 
                    transform=ax4.transAxes)
        elif insight.startswith("   "):
            ax4.text(0.1, y_pos, insight, ha='left', va='top', fontsize=11, 
                    transform=ax4.transAxes, style='italic')
        else:
            ax4.text(0.05, y_pos, insight, ha='left', va='top', fontsize=11, 
                    transform=ax4.transAxes)
        y_pos -= 0.04
    
    plt.tight_layout()
    plt.savefig('../results/final_summary_comparison.png', dpi=300, bbox_inches='tight')
    print("Saved final summary comparison plot")
    plt.close()

def create_methodology_summary():
    """Create methodology and validation summary."""
    
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
    
    # Plot 1: Experimental Setup
    ax1.axis('off')
    setup_text = [
        "EXPERIMENTAL METHODOLOGY",
        "",
        "DATASETS ANALYZED:",
        "   • Iris: 45 test samples, 4 features, 3 classes",
        "   • Wine: 36 test samples, 13 features, 3 classes", 
        "   • Synthetic: 600 test samples, 8 features, 4 classes",
        "",
        "🛠️  IMPLEMENTATIONS TESTED:",
        "   • Idris2 with dependent types",
        "   • Python reference implementation",
        "   • Python manual implementation",
        "   • Python scikit-learn",
        "",
        "SCIENTIFIC CONTROLS:",
        "   • Identical preprocessed datasets",
        "   • Standardized evaluation metrics",
        "   • Deterministic algorithms",
        "   • Fixed random seeds",
        "",
        "VALIDATION METHODS:",
        "   • Cross-implementation agreement",
        "   • Discrepancy analysis",
        "   • Statistical significance testing"
    ]
    
    y_pos = 0.95
    for text in setup_text:
        if text.startswith(""):
            ax1.text(0.5, y_pos, text, ha='center', va='top', fontsize=14, 
                    fontweight='bold', transform=ax1.transAxes)
        elif text.startswith(("", "🛠️", "", "")):
            ax1.text(0.05, y_pos, text, ha='left', va='top', fontsize=12, 
                    fontweight='bold', transform=ax1.transAxes)
        elif text.startswith("   •"):
            ax1.text(0.1, y_pos, text, ha='left', va='top', fontsize=10, 
                    transform=ax1.transAxes)
        else:
            ax1.text(0.05, y_pos, text, ha='left', va='top', fontsize=10, 
                    transform=ax1.transAxes)
        y_pos -= 0.04
    
    # Plot 2: Type Safety Benefits
    ax2.axis('off')
    benefits_text = [
        "IDRIS2 TYPE SAFETY BENEFITS",
        "",
        "COMPILE-TIME GUARANTEES:",
        "   • Array bounds checking",
        "   • Dimension compatibility",
        "   • k <= training_samples constraint",
        "",
        "MATHEMATICAL CORRECTNESS:",
        "   • Type-level proofs",
        "   • Dependent type constraints",
        "   • Zero runtime errors",
        "",
        "CODE CLARITY:",
        "   • Explicit algorithms",
        "   • Self-documenting types",
        "   • Formal specifications",
        "",
        "PERFORMANCE TRADE-OFFS:",
        "   • Lower accuracy on complex data",
        "   • Different algorithmic choices",
        "   • Limited optimization libraries"
    ]
    
    y_pos = 0.95
    for text in benefits_text:
        if text.startswith("🛡️"):
            ax2.text(0.5, y_pos, text, ha='center', va='top', fontsize=14, 
                    fontweight='bold', transform=ax2.transAxes)
        elif text.startswith(("", "")):
            ax2.text(0.05, y_pos, text, ha='left', va='top', fontsize=12, 
                    fontweight='bold', transform=ax2.transAxes)
        elif text.startswith("   •"):
            ax2.text(0.1, y_pos, text, ha='left', va='top', fontsize=10, 
                    transform=ax2.transAxes)
        else:
            ax2.text(0.05, y_pos, text, ha='left', va='top', fontsize=10, 
                    transform=ax2.transAxes)
        y_pos -= 0.04
    
    # Plot 3: Statistical Validation
    datasets = ['Iris', 'Wine', 'Synthetic']
    discrepancies = [0, 6, 131]  # Estimated for synthetic
    total_samples = [45, 36, 600]
    error_rates = [d/t*100 for d, t in zip(discrepancies, total_samples)]
    
    bars = ax3.bar(datasets, error_rates, color=['green', 'orange', 'red'], alpha=0.7)
    ax3.set_xlabel('Dataset', fontweight='bold', fontsize=12)
    ax3.set_ylabel('Discrepancy Rate (%)', fontweight='bold', fontsize=12)
    ax3.set_title('Implementation Discrepancy Analysis', fontweight='bold', fontsize=14)
    ax3.grid(True, alpha=0.3)
    
    # Add value labels
    for bar, rate, count in zip(bars, error_rates, discrepancies):
        height = bar.get_height()
        ax3.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                f'{rate:.1f}%\n({count} errors)', ha='center', va='bottom', 
                fontweight='bold', fontsize=10)
    
    # Plot 4: Recommendations
    ax4.axis('off')
    recommendations = [
        "RECOMMENDATIONS",
        "",
        "USE IDRIS2 KNN WHEN:",
        "   • Safety is critical",
        "   • Mathematical correctness required",
        "   • Educational/research purposes",
        "   • Small-medium datasets",
        "",
        "USE PYTHON KNN WHEN:",
        "   • Performance is critical",
        "   • Large, complex datasets",
        "   • Production systems",
        "   • Rapid prototyping needed",
        "",
        "RESEARCH IMPLICATIONS:",
        "   • Dataset size affects evaluation",
        "   • Type safety vs performance trade-off",
        "   • Need for comprehensive testing",
        "",
        "FUTURE WORK:",
        "   • Optimize Idris2 implementations",
        "   • Develop type-safe ML libraries",
        "   • Hybrid safety-performance approaches"
    ]
    
    y_pos = 0.95
    for text in recommendations:
        if text.startswith(""):
            ax4.text(0.5, y_pos, text, ha='center', va='top', fontsize=14, 
                    fontweight='bold', transform=ax4.transAxes)
        elif text.startswith(("", "", "")):
            ax4.text(0.05, y_pos, text, ha='left', va='top', fontsize=12, 
                    fontweight='bold', transform=ax4.transAxes)
        elif text.startswith("   •"):
            ax4.text(0.1, y_pos, text, ha='left', va='top', fontsize=10, 
                    transform=ax4.transAxes)
        else:
            ax4.text(0.05, y_pos, text, ha='left', va='top', fontsize=10, 
                    transform=ax4.transAxes)
        y_pos -= 0.04
    
    plt.tight_layout()
    plt.savefig('../results/methodology_and_recommendations.png', dpi=300, bbox_inches='tight')
    print("Saved methodology and recommendations plot")
    plt.close()

def main():
    print("Creating Final Summary Visualizations")
    print("=" * 50)
    
    create_final_summary()
    create_methodology_summary()
    
    print("\nFinal summary visualizations complete!")
    print("Results saved to ../results/:")
    print("   - final_summary_comparison.png")
    print("   - methodology_and_recommendations.png")

if __name__ == "__main__":
    main()
