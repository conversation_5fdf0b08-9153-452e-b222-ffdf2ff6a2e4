"""
Comprehensive data validation for KNN experiment.
Ensures all implementations use identical datasets with proper scientific controls.
"""

import numpy as np
import pandas as pd
import hashlib
import json
from pathlib import Path
from typing import Dict, List, Tuple, Any
import argparse

class DataValidator:
    """Validates data consistency across all implementations."""
    
    def __init__(self, data_dir: str = "../data", results_dir: str = "../results"):
        self.data_dir = Path(data_dir)
        self.results_dir = Path(results_dir)
        self.validation_report = {}
        
    def compute_data_hash(self, data: np.ndarray) -> str:
        """Compute SHA256 hash of data for integrity checking."""
        return hashlib.sha256(data.tobytes()).hexdigest()
    
    def load_and_validate_dataset(self, dataset_name: str) -> Dict[str, Any]:
        """Load and validate a specific dataset."""
        print(f"\nValidating {dataset_name} dataset...")
        
        validation_info = {
            'dataset_name': dataset_name,
            'files_found': {},
            'data_integrity': {},
            'statistics': {},
            'issues': []
        }
        
        # Check if all required files exist
        required_files = [f"{dataset_name}_train.csv", f"{dataset_name}_test.csv", f"{dataset_name}_full.csv"]
        
        for filename in required_files:
            filepath = self.data_dir / filename
            if filepath.exists():
                validation_info['files_found'][filename] = True
                print(f"  Found {filename}")
            else:
                validation_info['files_found'][filename] = False
                validation_info['issues'].append(f"Missing file: {filename}")
                print(f"  Missing {filename}")
        
        # If train and test files exist, validate them
        train_file = self.data_dir / f"{dataset_name}_train.csv"
        test_file = self.data_dir / f"{dataset_name}_test.csv"
        
        if train_file.exists() and test_file.exists():
            # Load data
            train_df = pd.read_csv(train_file)
            test_df = pd.read_csv(test_file)
            
            # Extract features and labels
            X_train = train_df.iloc[:, :-1].values
            y_train = train_df.iloc[:, -1].values
            X_test = test_df.iloc[:, :-1].values
            y_test = test_df.iloc[:, -1].values
            
            # Compute hashes for integrity
            validation_info['data_integrity'] = {
                'X_train_hash': self.compute_data_hash(X_train),
                'y_train_hash': self.compute_data_hash(y_train.astype(np.int32)),
                'X_test_hash': self.compute_data_hash(X_test),
                'y_test_hash': self.compute_data_hash(y_test.astype(np.int32))
            }
            
            # Compute statistics
            validation_info['statistics'] = {
                'train_samples': len(X_train),
                'test_samples': len(X_test),
                'features': X_train.shape[1],
                'classes': len(np.unique(np.concatenate([y_train, y_test]))),
                'train_class_distribution': {str(k): int(v) for k, v in zip(*np.unique(y_train, return_counts=True))},
                'test_class_distribution': {str(k): int(v) for k, v in zip(*np.unique(y_test, return_counts=True))},
                'feature_means': X_train.mean(axis=0).tolist(),
                'feature_stds': X_train.std(axis=0).tolist(),
                'feature_ranges': {
                    'min': X_train.min(axis=0).tolist(),
                    'max': X_train.max(axis=0).tolist()
                }
            }
            
            # Check for standardization (mean ≈ 0, std ≈ 1)
            means = np.abs(X_train.mean(axis=0))
            stds = X_train.std(axis=0)

            if not np.allclose(means, 0, atol=1e-6):
                validation_info['issues'].append(f"Features not properly centered (max mean: {means.max():.6f})")

            if not np.allclose(stds, 1, atol=1e-6):
                validation_info['issues'].append(f"Features not properly scaled (std range: {stds.min():.6f} - {stds.max():.6f})")
            
            # Check for missing values
            if np.isnan(X_train).any() or np.isnan(X_test).any():
                validation_info['issues'].append("Dataset contains NaN values")
            
            # Check label consistency
            train_labels = set(y_train)
            test_labels = set(y_test)
            if train_labels != test_labels:
                validation_info['issues'].append(f"Label mismatch: train={train_labels}, test={test_labels}")
            
            print(f"  {len(X_train)} train, {len(X_test)} test samples")
            print(f"  {X_train.shape[1]} features, {len(np.unique(y_train))} classes")
            
            if validation_info['issues']:
                print(f"  Warning: {len(validation_info['issues'])} issues found")
            else:
                print(f"  Data validation passed")
        
        return validation_info
    
    def validate_all_datasets(self) -> Dict[str, Any]:
        """Validate all available datasets."""
        print("Starting comprehensive data validation...")
        
        # Find all available datasets
        dataset_names = set()
        for file in self.data_dir.glob("*_train.csv"):
            dataset_name = file.stem.replace("_train", "")
            dataset_names.add(dataset_name)
        
        print(f"Found datasets: {sorted(dataset_names)}")
        
        validation_results = {}
        for dataset_name in sorted(dataset_names):
            validation_results[dataset_name] = self.load_and_validate_dataset(dataset_name)
        
        return validation_results
    
    def compare_datasets(self, validation_results: Dict[str, Any]) -> Dict[str, Any]:
        """Compare datasets for consistency."""
        print("\n Comparing datasets for consistency...")
        
        comparison = {
            'k_value_compatibility': {},
            'scaling_consistency': {},
            'size_progression': {}
        }
        
        # Check k=5 compatibility (k must be <= min training samples)
        min_train_samples = float('inf')
        for dataset_name, info in validation_results.items():
            if 'statistics' in info and 'train_samples' in info['statistics']:
                train_samples = info['statistics']['train_samples']
                min_train_samples = min(min_train_samples, train_samples)
                comparison['k_value_compatibility'][dataset_name] = {
                    'train_samples': train_samples,
                    'k5_compatible': train_samples >= 5
                }
        
        comparison['k_value_compatibility']['min_samples'] = min_train_samples
        comparison['k_value_compatibility']['k5_valid'] = min_train_samples >= 5
        
        # Check scaling consistency
        for dataset_name, info in validation_results.items():
            if 'statistics' in info:
                stats = info['statistics']
                means = np.array(stats['feature_means'])
                stds = np.array(stats['feature_stds'])
                
                comparison['scaling_consistency'][dataset_name] = {
                    'properly_centered': np.allclose(means, 0, atol=1e-6),
                    'properly_scaled': np.allclose(stds, 1, atol=1e-6),
                    'max_mean_deviation': float(np.abs(means).max()),
                    'std_range': [float(stds.min()), float(stds.max())]
                }
        
        # Analyze size progression
        sizes = []
        for dataset_name, info in validation_results.items():
            if 'statistics' in info:
                sizes.append((dataset_name, info['statistics']['train_samples']))
        
        sizes.sort(key=lambda x: x[1])
        comparison['size_progression'] = {
            'ordered_datasets': sizes,
            'size_ratios': []
        }
        
        for i in range(1, len(sizes)):
            ratio = sizes[i][1] / sizes[i-1][1]
            comparison['size_progression']['size_ratios'].append({
                'from': sizes[i-1][0],
                'to': sizes[i][0],
                'ratio': ratio
            })
        
        return comparison
    
    def generate_validation_report(self, validation_results: Dict[str, Any], comparison: Dict[str, Any]) -> None:
        """Generate comprehensive validation report."""
        print("\nGenerating validation report...")
        
        report = {
            'validation_timestamp': pd.Timestamp.now().isoformat(),
            'datasets': validation_results,
            'comparison': comparison,
            'summary': {
                'total_datasets': len(validation_results),
                'datasets_with_issues': sum(1 for info in validation_results.values() if info['issues']),
                'all_k5_compatible': comparison['k_value_compatibility']['k5_valid'],
                'all_properly_scaled': all(
                    info['scaling_consistency'][dataset]['properly_centered'] and 
                    info['scaling_consistency'][dataset]['properly_scaled']
                    for dataset, info in [(k, {'scaling_consistency': {k: v}}) for k, v in comparison['scaling_consistency'].items()]
                )
            }
        }
        
        # Save detailed JSON report
        report_file = self.results_dir / "data_validation_report.json"
        self.results_dir.mkdir(exist_ok=True)
        
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        print(f"Detailed report saved to {report_file}")
        
        # Print summary
        print("\n" + "="*60)
        print("DATA VALIDATION SUMMARY")
        print("="*60)
        
        for dataset_name, info in validation_results.items():
            status = "PASS" if not info['issues'] else f"FAIL ({len(info['issues'])} issues)"
            print(f"{dataset_name:20} {status}")
            
            if info['issues']:
                for issue in info['issues']:
                    print(f"  • {issue}")
        
        print(f"\nOverall Status:")
        print(f"  K=5 Compatible: {'YES' if report['summary']['all_k5_compatible'] else 'NO'}")
        print(f"  Properly Scaled: {'YES' if report['summary']['all_properly_scaled'] else 'NO'}")
        print(f"  Datasets with Issues: {report['summary']['datasets_with_issues']}/{report['summary']['total_datasets']}")
        
        return report
# main func may need to be modified lateer (note to self) - fixed
def main():
    parser = argparse.ArgumentParser(description="Validate KNN experiment datasets")
    parser.add_argument("--data-dir", default="../data", help="Data directory path")
    parser.add_argument("--results-dir", default="../results", help="Results directory path")
    
    args = parser.parse_args()
    
    validator = DataValidator(args.data_dir, args.results_dir)
    
    # Run validation
    validation_results = validator.validate_all_datasets()
    comparison = validator.compare_datasets(validation_results)
    report = validator.generate_validation_report(validation_results, comparison)
    
    # Exit with error code if issues found
    if report['summary']['datasets_with_issues'] > 0:
        print(f"\nValidation failed with {report['summary']['datasets_with_issues']} datasets having issues")
        exit(1)
    else:
        print(f"\nAll datasets passed validation")
        exit(0)

if __name__ == "__main__":
    main()
