"""
Data processing for KNN Iris classification experiment.
Downloads, processes, and saves Iris dataset for both Python and Idris implementations.

TODO: add support for other datasets besides iris
FIXME: the standardization might be overkill but sklearn docs recommend it
"""

import numpy as np
import pandas as pd
import os
from sklearn.datasets import load_iris
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
import argparse
# import matplotlib.pyplot as plt  # might need for debugging later

def create_data_directory():
    """Create data directory if it doesn't exist."""
    os.makedirs("../data", exist_ok=True)
    print("Data directory created/verified")

def load_and_process_iris():
    """Load and preprocess the Iris dataset."""
    # Load the Iris dataset
    iris = load_iris()
    X, y = iris.data, iris.target
    feature_names = iris.feature_names
    target_names = iris.target_names

    # print(f"Debug: X shape = {X.shape}")  # remove this later
    print(f"Loaded Iris dataset: {X.shape[0]} samples, {X.shape[1]} features")
    print(f"  Classes: {list(target_names)}")
    print(f"  Features: {list(feature_names)}")

    return X, y, feature_names, target_names

def split_and_scale_data(X, y, test_size=0.3, random_state=42):
    """Split data into train/test sets and standardize features."""
    # Split the data
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=test_size, random_state=random_state, stratify=y
    )
    
    # Standardize features (important for KNN)
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_test_scaled = scaler.transform(X_test)
    
    print(f"Data split: {len(X_train)} train, {len(X_test)} test samples")
    print(f"Features standardized (mean≈0, std≈1)")
    
    return X_train_scaled, X_test_scaled, y_train, y_test, scaler

def save_data(X_train, X_test, y_train, y_test, X_full, y_full, feature_names):
    """Save processed data to CSV files for both Python and Idris."""
    
    # Create DataFrames with proper column names
    train_df = pd.DataFrame(X_train, columns=feature_names)
    train_df['target'] = y_train
    
    test_df = pd.DataFrame(X_test, columns=feature_names)
    test_df['target'] = y_test
    
    full_df = pd.DataFrame(X_full, columns=feature_names)
    full_df['target'] = y_full
    
    # Save to CSV files
    train_df.to_csv("../data/iris_train.csv", index=False, float_format='%.6f')
    test_df.to_csv("../data/iris_test.csv", index=False, float_format='%.6f')
    full_df.to_csv("../data/iris_full.csv", index=False, float_format='%.6f')
    
    print(f"Saved training data: ../data/iris_train.csv ({len(train_df)} samples)")
    print(f"Saved test data: ../data/iris_test.csv ({len(test_df)} samples)")
    print(f"Saved full data: ../data/iris_full.csv ({len(full_df)} samples)")

def print_data_statistics(X_train, X_test, y_train, y_test, target_names):
    """Print dataset statistics."""
    print("\n" + "="*50)
    print("DATASET STATISTICS")
    print("="*50)
    
    print(f"Training set: {X_train.shape}")
    print(f"Test set: {X_test.shape}")
    print(f"Features: {X_train.shape[1]}")
    print(f"Classes: {len(target_names)}")
    
    print("\nClass distribution in training set:")
    unique, counts = np.unique(y_train, return_counts=True)
    for class_id, count in zip(unique, counts):
        print(f"  {target_names[class_id]}: {count} samples")
    
    print("\nFeature statistics (training set):")
    print(f"  Mean: {X_train.mean(axis=0)}")
    print(f"  Std:  {X_train.std(axis=0)}")
    print(f"  Min:  {X_train.min(axis=0)}")
    print(f"  Max:  {X_train.max(axis=0)}")

def main():
    parser = argparse.ArgumentParser(description="Process Iris dataset for KNN experiment")
    parser.add_argument("--test-size", type=float, default=0.3, 
                       help="Proportion of data for testing (default: 0.3)")
    parser.add_argument("--random-seed", type=int, default=42,
                       help="Random seed for reproducibility (default: 42)")
    args = parser.parse_args()
    
    print("KNN Iris Dataset Processing")
    print("="*40)
    
    # Create data directory
    create_data_directory()
    
    # Load and process data
    X, y, feature_names, target_names = load_and_process_iris()
    
    # Split and scale the data
    X_train, X_test, y_train, y_test, scaler = split_and_scale_data(
        X, y, test_size=args.test_size, random_state=args.random_seed
    )
    
    # Prepare full scaled dataset - need this for some analysis later
    # HACK: re-fitting scaler on full data, but it should be consistent
    X_full_scaled = scaler.fit_transform(X)

    # Save all data
    save_data(X_train, X_test, y_train, y_test, X_full_scaled, y, feature_names)
    
    # Print statistics
    print_data_statistics(X_train, X_test, y_train, y_test, target_names)
    
    print(f"\nData processing complete! Files saved to ../data/")
    print("Ready for KNN experiments in both Python and Idris")

if __name__ == "__main__":
    main()