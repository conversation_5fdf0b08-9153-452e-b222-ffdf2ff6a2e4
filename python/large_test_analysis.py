import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import json
from typing import Dict, List, Tuple
from scipy import stats
import argparse

# Set style for better plots
try:
    plt.style.use('seaborn-v0_8')
except OSError:
    plt.style.use('seaborn')

class LargeTestAnalyzer:
    """Analyze KNN implementations with large test datasets."""
    
    def __init__(self, results_dir: str = "../results"):
        self.results_dir = Path(results_dir)
        self.datasets = ['iris', 'wine', 'breast_cancer', 'synthetic']
        self.implementations = ['idris', 'reference', 'python_manual', 'python_sklearn']
        
    def load_predictions(self, dataset: str, implementation: str) -> np.ndarray:
        """Load predictions for a specific dataset and implementation."""
        filename = f"{implementation}_{dataset}_predictions.txt"
        filepath = self.results_dir / filename
        
        if filepath.exists():
            return np.loadtxt(filepath, dtype=int)
        else:
            print(f"Warning: Missing: {filename}")
            return np.array([])
    
    def load_true_labels(self, dataset: str) -> np.ndarray:
        """Load true labels for a dataset."""
        test_file = Path(f"../data/{dataset}_test.csv")
        if test_file.exists():
            df = pd.read_csv(test_file)
            return df.iloc[:, -1].values.astype(int)
        return np.array([])
    
    def calculate_accuracy(self, predictions: np.ndarray, true_labels: np.ndarray) -> float:
        """Calculate accuracy."""
        if len(predictions) == 0 or len(true_labels) == 0:
            return 0.0
        return np.mean(predictions == true_labels) * 100
    
    def calculate_agreement(self, pred1: np.ndarray, pred2: np.ndarray) -> float:
        """Calculate agreement between two prediction sets."""
        if len(pred1) == 0 or len(pred2) == 0:
            return 0.0
        return np.mean(pred1 == pred2) * 100
    
    def mcnemar_test(self, pred1: np.ndarray, pred2: np.ndarray, true_labels: np.ndarray) -> Tuple[float, float]:
        """Perform McNemar's test for statistical significance."""
        correct1 = (pred1 == true_labels)
        correct2 = (pred2 == true_labels)
        
        # Create contingency table
        both_correct = np.sum(correct1 & correct2)
        only_1_correct = np.sum(correct1 & ~correct2)
        only_2_correct = np.sum(~correct1 & correct2)
        both_wrong = np.sum(~correct1 & ~correct2)
        
        # McNemar's test statistic
        if only_1_correct + only_2_correct == 0:
            return 0.0, 1.0  # No difference
        
        chi2 = (abs(only_1_correct - only_2_correct) - 1)**2 / (only_1_correct + only_2_correct)
        p_value = 1 - stats.chi2.cdf(chi2, 1)
        
        return chi2, p_value
    
    def analyze_dataset_detailed(self, dataset: str) -> Dict:
        """Detailed analysis of a single dataset."""
        print(f"\nDetailed Analysis: {dataset.replace('_', ' ').title()}")
        print("=" * 60)
        
        # Load true labels
        true_labels = self.load_true_labels(dataset)
        if len(true_labels) == 0:
            print(f"Could not load true labels for {dataset}")
            return {}
        
        # Load predictions from all implementations
        predictions = {}
        for impl in self.implementations:
            pred = self.load_predictions(dataset, impl)
            if len(pred) > 0:
                predictions[impl] = pred
        
        if len(predictions) < 2:
            print(f"Need at least 2 implementations for comparison")
            return {}
        
        # Calculate accuracies
        accuracies = {}
        for impl, pred in predictions.items():
            acc = self.calculate_accuracy(pred, true_labels)
            accuracies[impl] = acc
            print(f"  {impl:15}: {acc:6.2f}% accuracy ({len(pred)} predictions)")
        
        # Statistical significance tests
        print(f"\nStatistical Significance Tests:")
        significance_results = {}
        impl_list = list(predictions.keys())
        
        for i, impl1 in enumerate(impl_list):
            for j, impl2 in enumerate(impl_list):
                if i < j:
                    chi2, p_value = self.mcnemar_test(predictions[impl1], predictions[impl2], true_labels)
                    pair_name = f"{impl1}_vs_{impl2}"
                    significance_results[pair_name] = {'chi2': chi2, 'p_value': p_value}
                    
                    significance = "***" if p_value < 0.001 else "**" if p_value < 0.01 else "*" if p_value < 0.05 else "ns"
                    print(f"  {impl1} vs {impl2}: χ² = {chi2:.3f}, p = {p_value:.4f} {significance}")
        
        # Agreement analysis
        print(f"\nImplementation Agreement:")
        agreements = {}
        for i, impl1 in enumerate(impl_list):
            for j, impl2 in enumerate(impl_list):
                if i < j:
                    agreement = self.calculate_agreement(predictions[impl1], predictions[impl2])
                    pair_name = f"{impl1}_vs_{impl2}"
                    agreements[pair_name] = agreement
                    print(f"  {impl1} vs {impl2}: {agreement:5.1f}% agreement")
        
        # Discrepancy analysis
        discrepancies = []
        if len(impl_list) >= 2:
            pred1 = predictions[impl_list[0]]
            pred2 = predictions[impl_list[1]]
            for idx, (p1, p2) in enumerate(zip(pred1, pred2)):
                if p1 != p2:
                    discrepancies.append({
                        'index': idx,
                        'impl1': impl_list[0],
                        'pred1': int(p1),
                        'impl2': impl_list[1], 
                        'pred2': int(p2),
                        'true_label': int(true_labels[idx])
                    })
        
        print(f"\nDiscrepancy Summary:")
        print(f"  Total discrepancies: {len(discrepancies)}/{len(true_labels)} ({len(discrepancies)/len(true_labels)*100:.1f}%)")
        
        return {
            'dataset': dataset,
            'test_samples': len(true_labels),
            'accuracies': accuracies,
            'agreements': agreements,
            'significance_tests': significance_results,
            'discrepancies': discrepancies[:10],  # First 10 discrepancies
            'total_discrepancies': len(discrepancies)
        }
    
    def create_large_test_plots(self, analysis_results: Dict) -> None:
        """Create comprehensive plots for large test analysis."""
        print("\nCreating large test analysis plots...")
        
        # Create figure with subplots
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
        
        # Plot 1: Sample Size vs Accuracy Differences
        datasets = []
        sample_sizes = []
        max_accuracy_diffs = []
        
        for dataset, results in analysis_results.items():
            if 'accuracies' in results and 'test_samples' in results:
                datasets.append(dataset.replace('_large_test', '').replace('_', ' ').title())
                sample_sizes.append(results['test_samples'])
                
                # Calculate max accuracy difference
                accuracies = list(results['accuracies'].values())
                max_diff = max(accuracies) - min(accuracies)
                max_accuracy_diffs.append(max_diff)
        
        scatter = ax1.scatter(sample_sizes, max_accuracy_diffs, s=150, alpha=0.7, c=range(len(sample_sizes)), cmap='viridis')
        ax1.set_xlabel('Test Samples', fontweight='bold', fontsize=12)
        ax1.set_ylabel('Max Accuracy Difference (%)', fontweight='bold', fontsize=12)
        ax1.set_title('Dataset Size vs Implementation Differences', fontweight='bold', fontsize=14)
        ax1.grid(True, alpha=0.3)
        
        # Add dataset labels
        for i, (size, diff) in enumerate(zip(sample_sizes, max_accuracy_diffs)):
            ax1.annotate(datasets[i], (size, diff), xytext=(5, 5), 
                        textcoords='offset points', fontsize=10, fontweight='bold')
        
        # Add trend line
        if len(sample_sizes) > 1:
            z = np.polyfit(sample_sizes, max_accuracy_diffs, 1)
            p = np.poly1d(z)
            ax1.plot(sample_sizes, p(sample_sizes), "r--", alpha=0.8, linewidth=2)
            
            # Add correlation coefficient
            corr = np.corrcoef(sample_sizes, max_accuracy_diffs)[0, 1]
            ax1.text(0.05, 0.95, f'Correlation: {corr:.3f}', transform=ax1.transAxes, 
                    bbox=dict(boxstyle="round,pad=0.3", facecolor="white", alpha=0.8))
        
        # Plot 2: Statistical Significance Heatmap
        datasets_short = [d.replace(' Large Test', '') for d in datasets]
        n_datasets = len(datasets_short)
        significance_matrix = np.zeros((n_datasets, 3))  # 3 comparison types
        
        comparison_labels = ['Ref vs Manual', 'Ref vs Sklearn', 'Manual vs Sklearn']
        
        for i, (dataset, results) in enumerate(analysis_results.items()):
            if 'significance_tests' in results:
                sig_tests = results['significance_tests']
                
                # Map significance tests to matrix
                if 'reference_vs_python_manual' in sig_tests:
                    p_val = sig_tests['reference_vs_python_manual']['p_value']
                    significance_matrix[i, 0] = -np.log10(max(p_val, 1e-10))  # -log10(p-value)
                
                if 'reference_vs_python_sklearn' in sig_tests:
                    p_val = sig_tests['reference_vs_python_sklearn']['p_value']
                    significance_matrix[i, 1] = -np.log10(max(p_val, 1e-10))
                
                if 'python_manual_vs_python_sklearn' in sig_tests:
                    p_val = sig_tests['python_manual_vs_python_sklearn']['p_value']
                    significance_matrix[i, 2] = -np.log10(max(p_val, 1e-10))
        
        im = ax2.imshow(significance_matrix, cmap='Reds', aspect='auto')
        ax2.set_xticks(range(3))
        ax2.set_yticks(range(n_datasets))
        ax2.set_xticklabels(comparison_labels, rotation=45, ha='right')
        ax2.set_yticklabels(datasets_short)
        ax2.set_title('Statistical Significance (-log₁₀ p-value)', fontweight='bold', fontsize=14)
        
        # Add significance threshold lines
        ax2.axhline(y=-0.5, color='blue', linestyle='--', alpha=0.5)  # p=0.05 threshold
        ax2.axhline(y=-0.5, color='red', linestyle='--', alpha=0.5)   # p=0.01 threshold
        
        # Add colorbar
        cbar = plt.colorbar(im, ax=ax2)
        cbar.set_label('-log₁₀(p-value)', fontweight='bold')
        
        # Plot 3: Agreement vs Sample Size
        agreement_data = []
        for dataset, results in analysis_results.items():
            if 'agreements' in results and 'test_samples' in results:
                # Get average agreement
                agreements = list(results['agreements'].values())
                if agreements:
                    avg_agreement = np.mean(agreements)
                    agreement_data.append((results['test_samples'], avg_agreement))
        
        if agreement_data:
            sizes, agreements = zip(*agreement_data)
            ax3.scatter(sizes, agreements, s=150, alpha=0.7, color='orange')
            ax3.set_xlabel('Test Samples', fontweight='bold', fontsize=12)
            ax3.set_ylabel('Average Agreement (%)', fontweight='bold', fontsize=12)
            ax3.set_title('Dataset Size vs Implementation Agreement', fontweight='bold', fontsize=14)
            ax3.grid(True, alpha=0.3)
            
            # Add dataset labels
            for i, (size, agr) in enumerate(zip(sizes, agreements)):
                ax3.annotate(datasets[i], (size, agr), xytext=(5, 5), 
                            textcoords='offset points', fontsize=10, fontweight='bold')
            
            # Add trend line
            if len(sizes) > 1:
                z = np.polyfit(sizes, agreements, 1)
                p = np.poly1d(z)
                ax3.plot(sizes, p(sizes), "r--", alpha=0.8, linewidth=2)
        
        # Plot 4: Discrepancy Rate Analysis
        discrepancy_rates = []
        for dataset, results in analysis_results.items():
            if 'total_discrepancies' in results and 'test_samples' in results:
                rate = (results['total_discrepancies'] / results['test_samples']) * 100
                discrepancy_rates.append(rate)
        
        if discrepancy_rates:
            bars = ax4.bar(datasets, discrepancy_rates, color='red', alpha=0.7)
            ax4.set_xlabel('Dataset', fontweight='bold', fontsize=12)
            ax4.set_ylabel('Discrepancy Rate (%)', fontweight='bold', fontsize=12)
            ax4.set_title('Implementation Discrepancy Rates', fontweight='bold', fontsize=14)
            ax4.grid(True, alpha=0.3)
            
            # Add value labels
            for bar, rate in zip(bars, discrepancy_rates):
                height = bar.get_height()
                ax4.text(bar.get_x() + bar.get_width()/2., height + 0.05,
                        f'{rate:.1f}%', ha='center', va='bottom', fontweight='bold')
        
        plt.tight_layout()
        plt.savefig(self.results_dir / 'large_test_statistical_analysis.png', dpi=300, bbox_inches='tight')
        print(f"Saved large test statistical analysis plot")
        plt.close()
    
    def run_analysis(self) -> None:
        """Run comprehensive large test analysis."""
        print("Large Test Dataset Statistical Analysis")
        print("=" * 70)
        
        analysis_results = {}
        for dataset in self.datasets:
            result = self.analyze_dataset_detailed(dataset)
            if result:
                analysis_results[dataset] = result
        
        if analysis_results:
            self.create_large_test_plots(analysis_results)
            
            print(f"\nLarge test statistical analysis complete!")
            print(f"Results saved to {self.results_dir}/")
            print(f"   - large_test_statistical_analysis.png")
            
            # Summary statistics
            total_samples = sum(r['test_samples'] for r in analysis_results.values())
            total_discrepancies = sum(r['total_discrepancies'] for r in analysis_results.values())
            
            print(f"\nSUMMARY STATISTICS:")
            print(f"   Total test samples analyzed: {total_samples:,}")
            print(f"   Total discrepancies found: {total_discrepancies:,}")
            print(f"   Overall discrepancy rate: {total_discrepancies/total_samples*100:.2f}%")
            
        else:
            print("No analysis results available")

def main():
    parser = argparse.ArgumentParser(description="Large test dataset statistical analysis")
    parser.add_argument("--results-dir", default="../results", help="Results directory")
    
    args = parser.parse_args()
    
    analyzer = LargeTestAnalyzer(args.results_dir)
    analyzer.run_analysis()

if __name__ == "__main__":
    main()
