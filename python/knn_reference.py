
"""
Reference KNN implementation for scientific integrity.
All other implementations must match this algorithm exactly.
"""

import numpy as np
import pandas as pd
from typing import List, Tu<PERSON>, Dict, Any
import json
import argparse
import time
from pathlib import Path

class ReferenceKNN:
    """Reference KNN implementation with deterministic behavior."""
    
    def __init__(self, k: int = 5, verbose: bool = False):
        self.k = k
        self.verbose = verbose
        self.debug_log = []
        
    def log_debug(self, message: str, data: Any = None):
        """Log debug information for validation."""
        entry = {"message": message}
        if data is not None:
            entry["data"] = data
        self.debug_log.append(entry)
        if self.verbose:
            print(f"DEBUG: {message}")
            if data is not None:
                print(f"       {data}")
    
    def euclidean_distance(self, v1: np.ndarray, v2: np.ndarray) -> float:
        """
        Standardized Euclidean distance calculation.
        Must match exactly across all implementations.
        """
        diff = v1 - v2
        squared_diff = diff * diff
        sum_squared = np.sum(squared_diff)
        distance = np.sqrt(sum_squared)
        return float(distance)
    
    def find_k_nearest_neighbors(self, query: np.ndarray, X_train: np.ndarray, y_train: np.ndarray) -> List[Tuple[float, int]]:
        """
        Find k nearest neighbors with deterministic tie-breaking.
        
        Tie-breaking rule: If distances are equal, use training sample index (lower index wins).
        """
        distances = []
        
        # Calculate distances to all training points
        for i in range(len(X_train)):
            dist = self.euclidean_distance(query, X_train[i])
            distances.append((dist, int(y_train[i]), i))  # (distance, label, index)
        
        # Sort by distance, then by index for deterministic tie-breaking
        distances.sort(key=lambda x: (x[0], x[2]))
        
        # Take first k neighbors and return (distance, label) pairs
        k_nearest = [(dist, label) for dist, label, _ in distances[:self.k]]
        
        return k_nearest
    
    def majority_vote(self, neighbors: List[Tuple[float, int]]) -> int:
        """
        Deterministic majority vote with tie-breaking.
        
        Tie-breaking rule: If counts are equal, return label with lowest numeric value.
        """
        # Count occurrences of each label
        label_counts = {}
        for _, label in neighbors:
            label_counts[label] = label_counts.get(label, 0) + 1
        
        # Find maximum count
        max_count = max(label_counts.values())
        
        # Get all labels with maximum count
        tied_labels = [label for label, count in label_counts.items() if count == max_count]
        
        # Return lowest label (deterministic tie-breaking)
        return min(tied_labels)
    
    def predict_single(self, query: np.ndarray, X_train: np.ndarray, y_train: np.ndarray) -> int:
        """Predict class for a single query point."""
        neighbors = self.find_k_nearest_neighbors(query, X_train, y_train)
        prediction = self.majority_vote(neighbors)
        
        # Log debug information for first prediction
        if len(self.debug_log) == 0:
            self.log_debug("First query point", query.tolist())
            self.log_debug("K nearest neighbors", neighbors)
            self.log_debug("Prediction", prediction)
        
        return prediction
    
    def predict(self, X_test: np.ndarray, X_train: np.ndarray, y_train: np.ndarray) -> np.ndarray:
        """Predict classes for all test points."""
        predictions = []
        
        for i, query in enumerate(X_test):
            pred = self.predict_single(query, X_train, y_train)
            predictions.append(pred)
            
            if self.verbose and (i + 1) % 10 == 0:
                print(f"Processed {i + 1}/{len(X_test)} test samples")
        
        return np.array(predictions)
    
    def calculate_accuracy(self, predictions: np.ndarray, y_true: np.ndarray) -> float:
        """Calculate accuracy."""
        correct = np.sum(predictions == y_true)
        total = len(predictions)
        return float(correct) / float(total)
    
    def save_debug_log(self, filepath: str):
        """Save debug log for validation."""
        with open(filepath, 'w') as f:
            json.dump(self.debug_log, f, indent=2, default=str)

def load_dataset(dataset_name: str) -> Tuple[np.ndarray, np.ndarray, np.ndarray, np.ndarray]:
    """Load standardized dataset."""
    train_df = pd.read_csv(f"../data/{dataset_name}_train.csv")
    test_df = pd.read_csv(f"../data/{dataset_name}_test.csv")
    
    X_train = train_df.iloc[:, :-1].values
    y_train = train_df.iloc[:, -1].values.astype(int)
    X_test = test_df.iloc[:, :-1].values
    y_test = test_df.iloc[:, -1].values.astype(int)
    
    return X_train, X_test, y_train, y_test

def run_reference_experiment(dataset_name: str, k: int = 5, verbose: bool = False) -> Dict[str, Any]:
    """Run reference KNN experiment."""
    print(f"Reference KNN Experiment: {dataset_name}")
    print("=" * 50)
    
    # Load data
    X_train, X_test, y_train, y_test = load_dataset(dataset_name)
    print(f"Loaded {len(X_train)} train, {len(X_test)} test samples")
    print(f"Features: {X_train.shape[1]}, Classes: {len(np.unique(y_train))}")
    
    # Initialize reference KNN
    knn = ReferenceKNN(k=k, verbose=verbose)
    
    # Make predictions with timing
    print(f"Running reference KNN (k={k})...")
    start_time = time.time()
    predictions = knn.predict(X_test, X_train, y_train)
    end_time = time.time()
    execution_time = end_time - start_time

    # Calculate accuracy
    accuracy = knn.calculate_accuracy(predictions, y_test)
    print(f"Reference Accuracy: {accuracy * 100:.1f}%")
    print(f"Execution Time: {execution_time:.4f} seconds")
    
    # Save results
    results_dir = Path("../results")
    results_dir.mkdir(exist_ok=True)
    
    # Save predictions
    pred_file = results_dir / f"reference_{dataset_name}_predictions.txt"
    np.savetxt(pred_file, predictions, fmt='%d')
    print(f"Saved predictions to {pred_file}")
    
    # Save debug log
    debug_file = results_dir / f"reference_{dataset_name}_debug.json"
    knn.save_debug_log(debug_file)
    print(f"Saved debug log to {debug_file}")
    
    # Create summary
    summary = {
        "dataset": dataset_name,
        "k": k,
        "train_samples": len(X_train),
        "test_samples": len(X_test),
        "features": X_train.shape[1],
        "classes": len(np.unique(y_train)),
        "accuracy": accuracy,
        "execution_time": execution_time,
        "predictions": predictions.tolist(),
        "first_query": X_test[0].tolist(),
        "first_neighbors": knn.debug_log[1]["data"] if len(knn.debug_log) > 1 else None,
        "first_prediction": int(predictions[0])
    }
    
    # Save summary
    summary_file = results_dir / f"reference_{dataset_name}_summary.json"
    with open(summary_file, 'w') as f:
        json.dump(summary, f, indent=2, default=str)
    print(f"Saved summary to {summary_file}")
    
    return summary

def validate_determinism(dataset_name: str, k: int = 5, runs: int = 3):
    """Validate that the reference implementation is deterministic."""
    print(f"\nValidating determinism with {runs} runs...")
    
    results = []
    for run in range(runs):
        print(f"  Run {run + 1}/{runs}")
        summary = run_reference_experiment(dataset_name, k, verbose=False)
        results.append(summary["predictions"])
    
    # Check if all runs produce identical results
    all_identical = all(np.array_equal(results[0], result) for result in results[1:])
    
    if all_identical:
        print("Reference implementation is deterministic")
    else:
        print("Reference implementation is NOT deterministic")
        for i, result in enumerate(results):
            print(f"  Run {i + 1}: {result[:5]}...")  # Show first 5 predictions
    
    return all_identical

def main():
    parser = argparse.ArgumentParser(description="Reference KNN implementation")
    parser.add_argument("--dataset", choices=['iris', 'wine', 'breast_cancer', 'synthetic_2000_8_4', 'synthetic_5000_12_5',
                                                        'iris_large_test', 'wine_large_test', 'breast_cancer_large_test', 'synthetic_5000_12_5_large_test'],
                       default='iris', help="Dataset to use")
    parser.add_argument("--k", type=int, default=5, help="Number of neighbors")
    parser.add_argument("--verbose", action="store_true", help="Verbose output")
    parser.add_argument("--validate-determinism", action="store_true", 
                       help="Validate deterministic behavior")
    
    args = parser.parse_args()
    
    if args.validate_determinism:
        validate_determinism(args.dataset, args.k)
    else:
        run_reference_experiment(args.dataset, args.k, args.verbose)

if __name__ == "__main__":
    main()
