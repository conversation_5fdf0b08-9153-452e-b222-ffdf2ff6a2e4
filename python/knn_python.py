"""
KNN.py - Main Python KNN implementation
Comprehensive K-Nearest Neighbors classifier for the comparative study with Idris implementation.

TODO: refactor the distance calculation - it's a bit messy
FIXME: handle edge cases better (what if k > n_samples?)
"""

import numpy as np
import pandas as pd
import time
import os
import argparse
from typing import List, Tuple, Optional, Union
from collections import Counter
from dataclasses import dataclass
from sklearn.neighbors import KNeighborsClassifier
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
from sklearn.datasets import load_iris
# import matplotlib.pyplot as plt  # might need for debugging
# import scipy.stats  # for future statistical tests

@dataclass
class KNNConfig:
    """Configuration for KNN classifier."""
    k: int = 5
    distance_metric: str = "euclidean"
    weights: str = "uniform"  # 'uniform' or 'distance' tested both earlier
    
@dataclass 
class ExperimentResults:
    """Results from KNN experiment."""
    predictions: np.ndarray
    accuracy: float
    execution_time: float
    confusion_matrix: np.ndarray
    classification_report: str

class TypeSafeKNN:
    """
    Manual KNN implementation with type hints for comparison with Idris.
    Demonstrates what type safety looks like in Python with explicit checking.
    """
    
    def __init__(self, config: KNNConfig):
        self.config = config
        self.X_train: Optional[np.ndarray] = None
        self.y_train: Optional[np.ndarray] = None
        self.n_features: Optional[int] = None
        self.n_classes: Optional[int] = None
        self.fitted: bool = False
        
    def _validate_input_shapes(self, X: np.ndarray, y: Optional[np.ndarray] = None) -> None:
        """Validate input tensor shapes - mimics Idris compile-time checking."""
        if X.ndim != 2:
            raise ValueError(f"X must be 2D array, got shape {X.shape}")
            
        if y is not None:
            if y.ndim != 1:
                raise ValueError(f"y must be 1D array, got shape {y.shape}")
            if len(X) != len(y):
                raise ValueError(f"X and y must have same length: {len(X)} vs {len(y)}")
                
        if self.fitted and X.shape[1] != self.n_features:
            raise ValueError(f"X has {X.shape[1]} features, expected {self.n_features}")
            
    def _euclidean_distance(self, x1: np.ndarray, x2: np.ndarray) -> float:
        """
        Calculate Euclidean distance between two feature vectors.
        Type-safe version with shape checking.

        NOTE: tried using scipy.spatial.distance but this is actually faster
        """
        if x1.shape != x2.shape:
            raise ValueError(f"Shape mismatch: {x1.shape} vs {x2.shape}")
        # this could probably be optimized but it works
        return float(np.sqrt(np.sum((x1 - x2) ** 2)))
        
    def _get_k_nearest_neighbors(self, query_point: np.ndarray) -> List[Tuple[float, int]]:
        """
        Find k nearest neighbors for a query point.
        Returns list of (distance, label) tuples.
        """
        if not self.fitted:
            raise RuntimeError("Model must be fitted before prediction")
            
        distances = []
        for i, train_point in enumerate(self.X_train):
            dist = self._euclidean_distance(query_point, train_point)
            distances.append((dist, self.y_train[i]))

        # Sort by distance and return k nearest
        # TODO: could use heapq for better performance on large datasets
        distances.sort(key=lambda x: x[0])
        return distances[:self.config.k]
        
    def _predict_single(self, query_point: np.ndarray) -> int:
        """Predict class for a single query point using majority vote."""
        neighbors = self._get_k_nearest_neighbors(query_point)
        
        if self.config.weights == "uniform":
            # Simple majority vote
            labels = [label for _, label in neighbors]
            return Counter(labels).most_common(1)[0][0]
        else:
            # Distance-weighted vote - this is a bit hacky but works
            label_weights = {}
            for dist, label in neighbors:
                weight = 1.0 / (dist + 1e-8)  # Avoid division by zero
                label_weights[label] = label_weights.get(label, 0) + weight
            return max(label_weights, key=label_weights.get)
            
    def fit(self, X: np.ndarray, y: np.ndarray) -> 'TypeSafeKNN':
        """
        Fit the KNN classifier to training data.
        Performs type and shape validation.
        """
        self._validate_input_shapes(X, y)
        
        self.X_train = X.copy()
        self.y_train = y.copy()
        self.n_features = X.shape[1]
        self.n_classes = len(np.unique(y))
        self.fitted = True
        
        # Validate k <= n_samples (mimics Idris constraint)
        if self.config.k > len(self.X_train):
            raise ValueError(f"k={self.config.k} cannot be greater than n_samples={len(self.X_train)}")
            
        print(f"Fitted KNN with k={self.config.k}, "
              f"n_samples={len(X)}, n_features={self.n_features}, n_classes={self.n_classes}")
        
        return self
        
    def predict(self, X: np.ndarray) -> np.ndarray:
        """Predict classes for test data."""
        self._validate_input_shapes(X)
        
        predictions = []
        for query_point in X:
            pred = self._predict_single(query_point)
            predictions.append(pred)
            
        return np.array(predictions)
        
    def predict_proba(self, X: np.ndarray) -> np.ndarray:
        """Predict class probabilities (simple version)."""
        self._validate_input_shapes(X)
        
        probabilities = []
        for query_point in X:
            neighbors = self._get_k_nearest_neighbors(query_point)
            labels = [label for _, label in neighbors]
            
            # Count occurrences for each class
            class_counts = np.zeros(self.n_classes)
            for label in labels:
                class_counts[label] += 1
                
            # Convert to probabilities
            probabilities.append(class_counts / self.config.k)
            
        return np.array(probabilities)

class KNNExperiment:
    """Main experiment class that orchestrates the KNN comparison."""
    
    def __init__(self, config: KNNConfig):
        self.config = config
        self.results = {}
        
    def load_iris_data(self, test_size: float = 0.3, random_state: int = 42) -> Tuple[np.ndarray, np.ndarray, np.ndarray, np.ndarray]:
        """Load and preprocess Iris dataset."""
        print("Loading Iris dataset...")

        iris = load_iris()
        X, y = iris.data, iris.target

        # Split the data
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=test_size, random_state=random_state, stratify=y
        )

        # Standardize features
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        X_test_scaled = scaler.transform(X_test)

        print(f"Dataset loaded: {len(X_train)} train, {len(X_test)} test samples")
        print(f"Features: {X.shape[1]}, Classes: {len(np.unique(y))}")

        return X_train_scaled, X_test_scaled, y_train, y_test
        
    def load_preprocessed_data(self, dataset_name: str = "iris") -> Tuple[np.ndarray, np.ndarray, np.ndarray, np.ndarray]:
        """Load data from preprocessed CSV files."""
        try:
            train_df = pd.read_csv(f"../data/{dataset_name}_train.csv")
            test_df = pd.read_csv(f"../data/{dataset_name}_test.csv")

            X_train = train_df.iloc[:, :-1].values
            y_train = train_df.iloc[:, -1].values.astype(int)

            X_test = test_df.iloc[:, :-1].values
            y_test = test_df.iloc[:, -1].values.astype(int)

            print(f"Loaded {dataset_name} data: {len(X_train)} train, {len(X_test)} test samples")
            print(f"Features: {X_train.shape[1]}, Classes: {len(np.unique(y_train))}")
            return X_train, X_test, y_train, y_test

        except FileNotFoundError:
            print(f"Warning: {dataset_name} data not found, loading from sklearn...")
            return self.load_iris_data()
            
    def run_manual_knn(self, X_train: np.ndarray, X_test: np.ndarray,
                      y_train: np.ndarray, y_test: np.ndarray) -> ExperimentResults:
        """Run manual KNN implementation."""
        print(f"\nRunning Manual KNN (k={self.config.k})...")

        start_time = time.time()

        # Create and fit model
        knn = TypeSafeKNN(self.config)
        knn.fit(X_train, y_train)

        # Make predictions
        # print(f"Debug: predicting {len(X_test)} samples")  # remove this later
        predictions = knn.predict(X_test)

        end_time = time.time()
        execution_time = end_time - start_time

        # Calculate metrics
        accuracy = accuracy_score(y_test, predictions)
        cm = confusion_matrix(y_test, predictions)
        n_classes = len(np.unique(y_test))
        target_names = [f'Class_{i}' for i in range(n_classes)]
        report = classification_report(y_test, predictions, target_names=target_names)

        print(f"Accuracy: {accuracy*100:.2f}%")
        print(f"Execution time: {execution_time:.4f}s")
        
        return ExperimentResults(
            predictions=predictions,
            accuracy=accuracy,
            execution_time=execution_time,
            confusion_matrix=cm,
            classification_report=report
        )
        
    def run_sklearn_knn(self, X_train: np.ndarray, X_test: np.ndarray,
                       y_train: np.ndarray, y_test: np.ndarray) -> ExperimentResults:
        """Run sklearn KNN implementation for comparison."""
        print(f"\nRunning Scikit-Learn KNN (k={self.config.k})...")

        start_time = time.time()

        # Create and fit model
        knn = KNeighborsClassifier(n_neighbors=self.config.k)
        knn.fit(X_train, y_train)

        # Make predictions
        predictions = knn.predict(X_test)

        end_time = time.time()
        execution_time = end_time - start_time

        # Calculate metrics
        accuracy = accuracy_score(y_test, predictions)
        cm = confusion_matrix(y_test, predictions)
        n_classes = len(np.unique(y_test))
        target_names = [f'Class_{i}' for i in range(n_classes)]
        report = classification_report(y_test, predictions, target_names=target_names)

        print(f"Accuracy: {accuracy*100:.2f}%")
        print(f"Execution time: {execution_time:.4f}s")
        
        return ExperimentResults(
            predictions=predictions,
            accuracy=accuracy,
            execution_time=execution_time,
            confusion_matrix=cm,
            classification_report=report
        )
        
    def save_results(self, manual_results: ExperimentResults, sklearn_results: ExperimentResults, dataset_name: str = "iris") -> None:
        """Save experiment results to files."""
        os.makedirs("../results", exist_ok=True)

        # Save predictions with dataset-specific names
        np.savetxt(f"../results/python_manual_{dataset_name}_predictions.txt", manual_results.predictions, fmt='%d')
        np.savetxt(f"../results/python_sklearn_{dataset_name}_predictions.txt", sklearn_results.predictions, fmt='%d')
        
        # Save detailed results
        with open(f"../results/python_{dataset_name}_experiment_results.txt", "w") as f:
            f.write("Python KNN Experiment Results\n")
            f.write("=" * 40 + "\n\n")
            
            f.write(f"Configuration:\n")
            f.write(f"  k = {self.config.k}\n")
            f.write(f"  distance_metric = {self.config.distance_metric}\n")
            f.write(f"  weights = {self.config.weights}\n\n")
            
            f.write("Manual Implementation:\n")
            f.write(f"  Accuracy: {manual_results.accuracy*100:.2f}%\n")
            f.write(f"  Execution time: {manual_results.execution_time:.4f}s\n\n")
            
            f.write("Scikit-Learn Implementation:\n")
            f.write(f"  Accuracy: {sklearn_results.accuracy*100:.2f}%\n")
            f.write(f"  Execution time: {sklearn_results.execution_time:.4f}s\n\n")
            
            f.write("Classification Report (Manual):\n")
            f.write(manual_results.classification_report)
            f.write("\n\nConfusion Matrix (Manual):\n")
            f.write(str(manual_results.confusion_matrix))
            
        print("Results saved to results/ directory")
        
    def compare_implementations(self, manual_results: ExperimentResults, 
                              sklearn_results: ExperimentResults) -> None:
        """Compare results between implementations."""
        print("\n" + "="*60)
        print("IMPLEMENTATION COMPARISON")
        print("="*60)
        
        # Accuracy comparison
        print(f"Manual KNN:    {manual_results.accuracy*100:.2f}% accuracy")
        print(f"Sklearn KNN:   {sklearn_results.accuracy*100:.2f}% accuracy")
        print(f"Difference:    {abs(manual_results.accuracy - sklearn_results.accuracy)*100:.2f}%")
        
        # Speed comparison
        print(f"\nExecution Time:")
        print(f"Manual KNN:    {manual_results.execution_time:.4f}s")
        print(f"Sklearn KNN:   {sklearn_results.execution_time:.4f}s")
        print(f"Speedup:       {manual_results.execution_time/sklearn_results.execution_time:.2f}x (manual vs sklearn)")
        
        # Agreement analysis
        agreement = np.mean(manual_results.predictions == sklearn_results.predictions) * 100
        print(f"\nPrediction Agreement: {agreement:.1f}%")
        
        if agreement < 100:
            print("\nDiscrepancies found:")
            for i, (m, s) in enumerate(zip(manual_results.predictions, sklearn_results.predictions)):
                if m != s:
                    print(f"  Sample {i}: Manual={m}, Sklearn={s}")
                    
    def run_full_experiment(self, dataset_name: str = "iris") -> None:
        """Run the complete KNN experiment."""
        print(f"Python KNN Classification Experiment - {dataset_name.upper()}")
        print("="*60)

        # Load data
        X_train, X_test, y_train, y_test = self.load_preprocessed_data(dataset_name)

        # Run experiments
        manual_results = self.run_manual_knn(X_train, X_test, y_train, y_test)
        sklearn_results = self.run_sklearn_knn(X_train, X_test, y_train, y_test)

        # Compare and save results
        self.compare_implementations(manual_results, sklearn_results)
        self.save_results(manual_results, sklearn_results, dataset_name)

        # Store results for external access
        self.results = {
            'manual': manual_results,
            'sklearn': sklearn_results
        }

        print("\nPython experiment completed successfully!")

def validate_k_value(k: int, max_k: int = 50) -> int:
    """Validate k parameter with constraints."""
    if k < 1:
        raise ValueError("k must be at least 1")
    if k > max_k:
        raise ValueError(f"k must be at most {max_k}")
    if k % 2 == 0:
        print(f"Warning: k={k} is even, which may cause ties in voting")
    return k

def main():
    """Main entry point for the KNN experiment."""
    parser = argparse.ArgumentParser(
        description="Python KNN implementation for comparison with Idris",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    
    parser.add_argument("--k", type=int, default=5,
                       help="Number of neighbors")
    parser.add_argument("--weights", choices=["uniform", "distance"], default="uniform",
                       help="Weight function for predictions")
    parser.add_argument("--distance", default="euclidean",
                       help="Distance metric to use")
    parser.add_argument("--benchmark", action="store_true",
                       help="Run benchmarking with multiple k values")
    parser.add_argument("--verbose", "-v", action="store_true",
                       help="Enable verbose output")
    parser.add_argument("--dataset", choices=['iris', 'wine', 'breast_cancer', 'synthetic'], default='iris',
                       help="Dataset to use (default: iris)")

    args = parser.parse_args()
    
    try:
        # Validate parameters
        k = validate_k_value(args.k)
        
        if args.benchmark:
            print("Running benchmark with multiple k values...")
            k_values = [1, 3, 5, 7, 9, 11]
            best_k = 5
            best_accuracy = 0

            for k_val in k_values:
                config = KNNConfig(k=k_val, weights=args.weights, distance_metric=args.distance)
                experiment = KNNExperiment(config)

                print(f"\n--- Testing k={k_val} ---")
                X_train, X_test, y_train, y_test = experiment.load_preprocessed_data(args.dataset)
                manual_results = experiment.run_manual_knn(X_train, X_test, y_train, y_test)

                if manual_results.accuracy > best_accuracy:
                    best_accuracy = manual_results.accuracy
                    best_k = k_val

            print(f"\nBest k value: {best_k} with accuracy {best_accuracy*100:.2f}%")
            
        else:
            # Single experiment run
            config = KNNConfig(k=k, weights=args.weights, distance_metric=args.distance)
            experiment = KNNExperiment(config)
            experiment.run_full_experiment(args.dataset)
            
    except Exception as e:
        print(f"Error: {e}")
        return 1
        
    return 0

if __name__ == "__main__":
    exit(main())