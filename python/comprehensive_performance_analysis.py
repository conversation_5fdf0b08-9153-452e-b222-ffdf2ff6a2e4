#!/usr/bin/env python3
"""
Comprehensive performance analysis combining accuracy and execution time.
Creates final comparison plots showing the accuracy vs speed trade-offs.

TODO: make the plots look better
FIXME: hardcoded timing data is a bit hacky but works for now
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import json
from typing import Dict, List, Tuple
import argparse
# import scipy.stats  # might need this later

# Set style for better plots
try:
    plt.style.use('seaborn-v0_8')
except OSError:
    plt.style.use('seaborn')

class ComprehensivePerformanceAnalyzer:
    """Analyze both accuracy and execution time performance."""
    
    def __init__(self, results_dir: str = "../results"):
        self.results_dir = Path(results_dir)
        self.datasets = ['iris_large_test', 'wine_large_test', 'breast_cancer_large_test', 'synthetic_5000_12_5_large_test']
        self.implementations = ['idris', 'reference', 'python_manual', 'python_sklearn']
        
    def load_predictions(self, dataset: str, implementation: str) -> np.ndarray:
        """Load predictions for a specific dataset and implementation."""
        filename = f"{implementation}_{dataset}_predictions.txt"
        filepath = self.results_dir / filename
        
        if filepath.exists():
            return np.loadtxt(filepath, dtype=int)
        else:
            return np.array([])
    
    def load_true_labels(self, dataset: str) -> np.ndarray:
        """Load true labels for a dataset."""
        test_file = Path(f"../data/{dataset}_test.csv")
        if test_file.exists():
            df = pd.read_csv(test_file)
            return df.iloc[:, -1].values.astype(int)
        return np.array([])
    
    def calculate_accuracy(self, predictions: np.ndarray, true_labels: np.ndarray) -> float:
        """Calculate accuracy."""
        if len(predictions) == 0 or len(true_labels) == 0:
            return 0.0
        return np.mean(predictions == true_labels) * 100
    
    def get_performance_data(self) -> Dict:
        """Get comprehensive performance data for all implementations."""
        
        # Hardcoded timing results from our analysis - should probably load this from file
        timing_data = {
            'iris_large_test': {
                'idris': 0.4486, 'reference': 0.7260, 'python_manual': 1.4794, 'python_sklearn': 0.9863
            },
            'wine_large_test': {
                'idris': 0.1444, 'reference': 0.6946, 'python_manual': 1.2824, 'python_sklearn': 0.8550
            },
            'breast_cancer_large_test': {
                'idris': 0.3180, 'reference': 1.1148, 'python_manual': 1.5581, 'python_sklearn': 1.0387
            },
            'synthetic_5000_12_5_large_test': {
                'idris': 15.4081, 'reference': 27.3725, 'python_manual': 18.2356, 'python_sklearn': 12.1571
            }
        }
        
        performance_data = {}
        
        for dataset in self.datasets:
            # Load true labels
            true_labels = self.load_true_labels(dataset)
            if len(true_labels) == 0:
                continue
            
            dataset_data = {
                'test_samples': len(true_labels),
                'implementations': {}
            }
            
            # Get accuracy and timing for each implementation
            for impl in self.implementations:
                predictions = self.load_predictions(dataset, impl)
                if len(predictions) > 0:
                    accuracy = self.calculate_accuracy(predictions, true_labels)
                    exec_time = timing_data.get(dataset, {}).get(impl, 0)
                    
                    dataset_data['implementations'][impl] = {
                        'accuracy': accuracy,
                        'execution_time': exec_time,
                        'predictions_count': len(predictions)
                    }
            
            performance_data[dataset] = dataset_data
        
        return performance_data
    
    def create_comprehensive_plots(self, performance_data: Dict) -> None:
        """Create comprehensive performance analysis plots."""
        print("\nCreating comprehensive performance plots...")
        
        # Create figure with subplots
        fig = plt.figure(figsize=(20, 16))
        
        # Plot 1: Accuracy vs Execution Time Scatter (top left)
        ax1 = plt.subplot(2, 3, 1)
        
        colors = {'idris': '#FF6B6B', 'reference': '#4ECDC4', 'python_manual': '#45B7D1', 'python_sklearn': '#96CEB4'}
        markers = {'idris': 'o', 'reference': 's', 'python_manual': '^', 'python_sklearn': 'D'}
        
        for dataset, data in performance_data.items():
            dataset_name = dataset.replace('_large_test', '').replace('_', ' ').title()
            for impl, metrics in data['implementations'].items():
                ax1.scatter(metrics['execution_time'], metrics['accuracy'], 
                           s=100, alpha=0.7, color=colors[impl], marker=markers[impl],
                           label=f"{impl.replace('_', ' ').title()}" if dataset == list(performance_data.keys())[0] else "")
        
        ax1.set_xlabel('Execution Time (seconds)', fontweight='bold', fontsize=12)
        ax1.set_ylabel('Accuracy (%)', fontweight='bold', fontsize=12)
        ax1.set_title('Accuracy vs Execution Time Trade-off', fontweight='bold', fontsize=14)
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        ax1.set_xscale('log')
        
        # Plot 2: Performance Efficiency (Accuracy/Time) (top middle)
        ax2 = plt.subplot(2, 3, 2)
        
        datasets = []
        efficiency_data = {impl: [] for impl in self.implementations}
        
        for dataset, data in performance_data.items():
            datasets.append(dataset.replace('_large_test', '').replace('_', ' ').title())
            for impl in self.implementations:
                if impl in data['implementations']:
                    metrics = data['implementations'][impl]
                    efficiency = metrics['accuracy'] / metrics['execution_time'] if metrics['execution_time'] > 0 else 0
                    efficiency_data[impl].append(efficiency)
                else:
                    efficiency_data[impl].append(0)
        
        x = np.arange(len(datasets))
        width = 0.2
        
        for i, impl in enumerate(self.implementations):
            efficiencies = efficiency_data[impl]
            bars = ax2.bar(x + i * width, efficiencies, width, 
                          label=impl.replace('_', ' ').title(), 
                          color=colors[impl], alpha=0.8)
            
            # Add value labels
            for bar, eff in zip(bars, efficiencies):
                if eff > 0:
                    height = bar.get_height()
                    ax2.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                            f'{eff:.1f}', ha='center', va='bottom', fontweight='bold', fontsize=9)
        
        ax2.set_xlabel('Dataset', fontweight='bold', fontsize=12)
        ax2.set_ylabel('Efficiency (Accuracy/Time)', fontweight='bold', fontsize=12)
        ax2.set_title('Performance Efficiency Comparison', fontweight='bold', fontsize=14)
        ax2.set_xticks(x + width * 1.5)
        ax2.set_xticklabels(datasets, rotation=45, ha='right')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        # Plot 3: Dataset Complexity vs Performance Gap (top right)
        ax3 = plt.subplot(2, 3, 3)
        
        sample_sizes = []
        idris_python_gaps = []
        
        for dataset, data in performance_data.items():
            if 'idris' in data['implementations'] and 'python_sklearn' in data['implementations']:
                sample_sizes.append(data['test_samples'])
                idris_acc = data['implementations']['idris']['accuracy']
                python_acc = data['implementations']['python_sklearn']['accuracy']
                gap = python_acc - idris_acc
                idris_python_gaps.append(gap)
        
        if sample_sizes and idris_python_gaps:
            ax3.scatter(sample_sizes, idris_python_gaps, s=150, alpha=0.7, color='#FF6B6B')
            ax3.set_xlabel('Test Samples (Dataset Size)', fontweight='bold', fontsize=12)
            ax3.set_ylabel('Python Advantage (% points)', fontweight='bold', fontsize=12)
            ax3.set_title('Dataset Size vs Accuracy Gap', fontweight='bold', fontsize=14)
            ax3.grid(True, alpha=0.3)
            
            # Add trend line
            if len(sample_sizes) > 1:
                z = np.polyfit(sample_sizes, idris_python_gaps, 1)
                p = np.poly1d(z)
                ax3.plot(sample_sizes, p(sample_sizes), "r--", alpha=0.8, linewidth=2)
                
                # Add correlation
                corr = np.corrcoef(sample_sizes, idris_python_gaps)[0, 1]
                ax3.text(0.05, 0.95, f'Correlation: {corr:.3f}', transform=ax3.transAxes, 
                        bbox=dict(boxstyle="round,pad=0.3", facecolor="white", alpha=0.8))
            
            # Add dataset labels
            for i, (size, gap) in enumerate(zip(sample_sizes, idris_python_gaps)):
                ax3.annotate(datasets[i], (size, gap), xytext=(5, 5), 
                            textcoords='offset points', fontsize=10, fontweight='bold')
        
        # Plot 4: Implementation Comparison Matrix (bottom left)
        ax4 = plt.subplot(2, 3, 4)
        
        # Create comparison matrix (accuracy - execution_time_penalty)
        comparison_matrix = np.zeros((len(datasets), len(self.implementations)))
        
        for i, dataset in enumerate(performance_data.keys()):
            data = performance_data[dataset]
            for j, impl in enumerate(self.implementations):
                if impl in data['implementations']:
                    metrics = data['implementations'][impl]
                    # Normalize execution time penalty (higher time = lower score)
                    max_time = max(m['execution_time'] for m in data['implementations'].values())
                    time_penalty = (metrics['execution_time'] / max_time) * 20  # Scale to 20 points
                    score = metrics['accuracy'] - time_penalty
                    comparison_matrix[i, j] = score
        
        im = ax4.imshow(comparison_matrix, cmap='RdYlGn', aspect='auto')
        ax4.set_xticks(range(len(self.implementations)))
        ax4.set_yticks(range(len(datasets)))
        ax4.set_xticklabels([impl.replace('_', ' ').title() for impl in self.implementations], rotation=45, ha='right')
        ax4.set_yticklabels(datasets)
        ax4.set_title('Overall Performance Score\n(Accuracy - Time Penalty)', fontweight='bold', fontsize=14)
        
        # Add text annotations
        for i in range(len(datasets)):
            for j in range(len(self.implementations)):
                score = comparison_matrix[i, j]
                if score != 0:
                    ax4.text(j, i, f'{score:.1f}', ha='center', va='center', 
                            fontweight='bold', color='white' if score < 70 else 'black')
        
        plt.colorbar(im, ax=ax4)
        
        # Plot 5: Speed vs Accuracy Trade-off Analysis (bottom middle)
        ax5 = plt.subplot(2, 3, 5)
        
        # Create Pareto frontier analysis
        all_accuracies = []
        all_times = []
        all_labels = []
        
        for dataset, data in performance_data.items():
            dataset_short = dataset.replace('_large_test', '').replace('_', ' ').title()
            for impl, metrics in data['implementations'].items():
                all_accuracies.append(metrics['accuracy'])
                all_times.append(metrics['execution_time'])
                all_labels.append(f"{dataset_short}\n{impl.replace('_', ' ').title()}")
        
        scatter = ax5.scatter(all_times, all_accuracies, s=100, alpha=0.7, 
                             c=range(len(all_accuracies)), cmap='viridis')
        ax5.set_xlabel('Execution Time (seconds)', fontweight='bold', fontsize=12)
        ax5.set_ylabel('Accuracy (%)', fontweight='bold', fontsize=12)
        ax5.set_title('Speed vs Accuracy Trade-off Space', fontweight='bold', fontsize=14)
        ax5.grid(True, alpha=0.3)
        ax5.set_xscale('log')
        
        # Add some labels for extreme points
        if all_accuracies and all_times:
            # Highest accuracy
            max_acc_idx = np.argmax(all_accuracies)
            ax5.annotate(f'Highest Acc\n{all_labels[max_acc_idx]}', 
                        (all_times[max_acc_idx], all_accuracies[max_acc_idx]),
                        xytext=(10, 10), textcoords='offset points', fontsize=8,
                        bbox=dict(boxstyle="round,pad=0.3", facecolor="yellow", alpha=0.7))
            
            # Fastest
            min_time_idx = np.argmin(all_times)
            ax5.annotate(f'Fastest\n{all_labels[min_time_idx]}', 
                        (all_times[min_time_idx], all_accuracies[min_time_idx]),
                        xytext=(10, -20), textcoords='offset points', fontsize=8,
                        bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgreen", alpha=0.7))
        
        # Plot 6: Summary Statistics (bottom right)
        ax6 = plt.subplot(2, 3, 6)
        ax6.axis('tight')
        ax6.axis('off')
        
        # Create summary statistics table
        summary_stats = []
        summary_stats.append(['Implementation', 'Avg Accuracy', 'Avg Time', 'Efficiency', 'Rank'])
        
        impl_stats = {}
        for impl in self.implementations:
            accuracies = []
            times = []
            for data in performance_data.values():
                if impl in data['implementations']:
                    accuracies.append(data['implementations'][impl]['accuracy'])
                    times.append(data['implementations'][impl]['execution_time'])
            
            if accuracies and times:
                avg_acc = np.mean(accuracies)
                avg_time = np.mean(times)
                efficiency = avg_acc / avg_time
                impl_stats[impl] = {
                    'avg_accuracy': avg_acc,
                    'avg_time': avg_time,
                    'efficiency': efficiency
                }
        
        # Rank by efficiency
        ranked_impls = sorted(impl_stats.items(), key=lambda x: x[1]['efficiency'], reverse=True)
        
        for rank, (impl, stats) in enumerate(ranked_impls, 1):
            row = [
                impl.replace('_', ' ').title(),
                f"{stats['avg_accuracy']:.1f}%",
                f"{stats['avg_time']:.3f}s",
                f"{stats['efficiency']:.1f}",
                str(rank)
            ]
            summary_stats.append(row)
        
        if len(summary_stats) > 1:
            table = ax6.table(cellText=summary_stats[1:], colLabels=summary_stats[0], 
                             cellLoc='center', loc='center')
            table.auto_set_font_size(False)
            table.set_fontsize(10)
            table.scale(1.2, 1.8)
            
            # Style the table
            for i in range(len(summary_stats[0])):
                table[(0, i)].set_facecolor('#4ECDC4')
                table[(0, i)].set_text_props(weight='bold')
            
            # Color code the efficiency column
            for i in range(1, len(summary_stats)):
                efficiency = float(summary_stats[i][3])
                if efficiency > 100:
                    table[(i, 3)].set_facecolor('#90EE90')  # Light green
                elif efficiency > 50:
                    table[(i, 3)].set_facecolor('#FFFFE0')  # Light yellow
                else:
                    table[(i, 3)].set_facecolor('#FFB6C1')  # Light red
        
        ax6.set_title('Performance Summary Rankings', fontweight='bold', fontsize=14, pad=20)
        
        plt.tight_layout()
        plt.savefig(self.results_dir / 'comprehensive_performance_analysis.png', dpi=300, bbox_inches='tight')
        print(f"Saved comprehensive performance analysis plot")
        plt.close()
    
    def run_analysis(self) -> None:
        """Run comprehensive performance analysis."""
        print("Comprehensive Performance Analysis (Accuracy + Execution Time)")
        print("=" * 80)
        
        performance_data = self.get_performance_data()
        
        if performance_data:
            self.create_comprehensive_plots(performance_data)
            
            print(f"\nComprehensive performance analysis complete!")
            print(f"Results saved to {self.results_dir}/")
            print(f"   - comprehensive_performance_analysis.png")
            
            # Print summary
            print(f"\nPERFORMANCE SUMMARY:")
            for dataset, data in performance_data.items():
                dataset_name = dataset.replace('_large_test', '').replace('_', ' ').title()
                print(f"\n{dataset_name} ({data['test_samples']} samples):")
                
                # Sort by efficiency
                impl_metrics = []
                for impl, metrics in data['implementations'].items():
                    efficiency = metrics['accuracy'] / metrics['execution_time'] if metrics['execution_time'] > 0 else 0
                    impl_metrics.append((impl, metrics['accuracy'], metrics['execution_time'], efficiency))
                
                impl_metrics.sort(key=lambda x: x[3], reverse=True)
                
                for impl, acc, time, eff in impl_metrics:
                    print(f"  {impl:15}: {acc:5.1f}% accuracy, {time:7.3f}s, efficiency: {eff:5.1f}")
        else:
            print("No performance data available")

def main():
    parser = argparse.ArgumentParser(description="Comprehensive performance analysis")
    parser.add_argument("--results-dir", default="../results", help="Results directory")
    
    args = parser.parse_args()
    
    analyzer = ComprehensivePerformanceAnalyzer(args.results_dir)
    analyzer.run_analysis()

if __name__ == "__main__":
    main()
