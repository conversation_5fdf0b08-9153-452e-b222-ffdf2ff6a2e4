#!/usr/bin/env python3
"""
Comprehensive comparison plots including Idris2 KNN implementations.
Shows differences between type-safe Idris and Python implementations.
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import json
from typing import Dict, List, Tuple
import argparse

# Set style for better plots
try:
    plt.style.use('seaborn-v0_8')
except OSError:
    plt.style.use('seaborn')
sns.set_palette("husl")

class IdrisComparisonAnalyzer:
    """Comprehensive analysis including Idris2 implementations."""
    
    def __init__(self, results_dir: str = "../results"):
        self.results_dir = Path(results_dir)
        # Updated datasets with progressive sizes including synthetic
        self.datasets = ['iris', 'wine', 'breast_cancer', 'synthetic']
        self.implementations = ['idris', 'reference', 'python_manual', 'python_sklearn']  # Include real Idris results
        
    def load_predictions(self, dataset: str, implementation: str) -> np.ndarray:
        """Load predictions for a specific dataset and implementation."""
        filename = f"{implementation}_{dataset}_predictions.txt"
        filepath = self.results_dir / filename
        
        if filepath.exists():
            return np.loadtxt(filepath, dtype=int)
        else:
            print(f"Warning: Missing: {filename}")
            return np.array([])
    
    def load_true_labels(self, dataset: str) -> np.ndarray:
        """Load true labels for a dataset."""
        test_file = Path(f"../data/{dataset}_test.csv")
        if test_file.exists():
            df = pd.read_csv(test_file)
            return df.iloc[:, -1].values.astype(int)
        return np.array([])
    
    def calculate_accuracy(self, predictions: np.ndarray, true_labels: np.ndarray) -> float:
        """Calculate accuracy."""
        if len(predictions) == 0 or len(true_labels) == 0:
            return 0.0
        return np.mean(predictions == true_labels) * 100
    
    def calculate_agreement(self, pred1: np.ndarray, pred2: np.ndarray) -> float:
        """Calculate agreement between two prediction sets."""
        if len(pred1) == 0 or len(pred2) == 0:
            return 0.0
        return np.mean(pred1 == pred2) * 100
    
    def analyze_dataset(self, dataset: str) -> Dict:
        """Analyze a single dataset across all implementations."""
        print(f"\nAnalyzing {dataset} dataset...")

        # Load true labels
        true_labels = self.load_true_labels(dataset)
        if len(true_labels) == 0:
            print(f"Error: Could not load true labels for {dataset}")
            return {}
        
        # Load predictions from all implementations
        predictions = {}
        for impl in self.implementations:
            pred = self.load_predictions(dataset, impl)
            if len(pred) > 0:
                predictions[impl] = pred
        
        if not predictions:
            print(f"Error: No predictions found for {dataset}")
            return {}

        # Calculate accuracies
        accuracies = {}
        for impl, pred in predictions.items():
            acc = self.calculate_accuracy(pred, true_labels)
            accuracies[impl] = acc
            print(f"  {impl:15}: {acc:5.1f}% accuracy")
        
        # Calculate agreements with Idris as reference
        agreements = {}
        if 'idris' in predictions:
            idris_pred = predictions['idris']
            for impl, pred in predictions.items():
                if impl != 'idris':
                    agreement = self.calculate_agreement(idris_pred, pred)
                    agreements[f"idris_vs_{impl}"] = agreement
                    print(f"  Idris vs {impl}: {agreement:5.1f}% agreement")
        
        # Find discrepancies between Idris and Reference
        discrepancies = []
        if 'idris' in predictions and 'reference' in predictions:
            idris_pred = predictions['idris']
            ref_pred = predictions['reference']
            for idx, (i_pred, r_pred) in enumerate(zip(idris_pred, ref_pred)):
                if i_pred != r_pred:
                    discrepancies.append({
                        'index': idx,
                        'idris_pred': int(i_pred),
                        'reference_pred': int(r_pred),
                        'true_label': int(true_labels[idx])
                    })
        
        return {
            'dataset': dataset,
            'test_samples': len(true_labels),
            'accuracies': accuracies,
            'agreements': agreements,
            'discrepancies': discrepancies[:20],  # First 20 discrepancies
            'total_discrepancies': len(discrepancies)
        }
    
    def create_comprehensive_plots(self, analysis_results: Dict) -> None:
        """Create comprehensive comparison plots including Idris."""
        print("\nCreating Idris comparison plots...")
        
        # Prepare data for plotting
        datasets = []
        accuracies_data = []
        
        for dataset, results in analysis_results.items():
            if 'accuracies' in results:
                datasets.append(dataset.replace('_', ' ').title())
                accuracies_data.append(results['accuracies'])
        
        if not accuracies_data:
            print("No data available for plotting")
            return
        
        # Create figure with subplots
        fig = plt.figure(figsize=(22, 18))
        
        # Plot 1: Accuracy comparison (top left)
        ax1 = plt.subplot(3, 2, 1)
        implementations = list(accuracies_data[0].keys())
        x = np.arange(len(datasets))
        width = 0.2
        colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4']
        
        for i, impl in enumerate(implementations):
            accuracies = [data.get(impl, 0) for data in accuracies_data]
            bars = ax1.bar(x + i * width, accuracies, width, 
                          label=impl.replace('_', ' ').title(), 
                          color=colors[i % len(colors)], alpha=0.8)
            
            # Add value labels on bars
            for bar, acc in zip(bars, accuracies):
                height = bar.get_height()
                ax1.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                        f'{acc:.1f}%', ha='center', va='bottom', fontweight='bold', fontsize=10)
        
        ax1.set_xlabel('Dataset', fontweight='bold')
        ax1.set_ylabel('Accuracy (%)', fontweight='bold')
        ax1.set_title('KNN Accuracy: Idris2 vs Python Implementations', fontweight='bold', fontsize=14)
        ax1.set_xticks(x + width * 1.5)
        ax1.set_xticklabels(datasets, rotation=45, ha='right')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        ax1.set_ylim(0, 105)
        
        # Plot 2: Idris vs Others Agreement (top right)
        ax2 = plt.subplot(3, 2, 2)
        agreement_data = []
        agreement_labels = []

        for dataset, results in analysis_results.items():
            if 'agreements' in results:
                for pair, agreement in results['agreements'].items():
                    if 'idris_vs' in pair:
                        other_impl = pair.replace('idris_vs_', '').replace('_', ' ').title()
                        agreement_data.append(agreement)
                        agreement_labels.append(f"{dataset.replace('_', ' ').title()}\nvs {other_impl}")

        if agreement_data:
            # Create colors cycling through the palette
            colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FECA57', '#FF9FF3']
            bar_colors = [colors[i % len(colors)] for i in range(len(agreement_data))]

            bars = ax2.bar(range(len(agreement_data)), agreement_data,
                          color=bar_colors, alpha=0.8, width=0.8)
            ax2.set_xlabel('Comparison', fontweight='bold')
            ax2.set_ylabel('Agreement (%)', fontweight='bold')
            ax2.set_title('Idris2 Agreement with Other Implementations', fontweight='bold', fontsize=14)
            ax2.set_xticks(range(len(agreement_data)))
            ax2.set_xticklabels(agreement_labels, rotation=45, ha='right', fontsize=9)

            # Adjust margins to ensure all labels are visible
            ax2.margins(x=0.02)
            ax2.grid(True, alpha=0.3)

            # Set y-limits to show all bars properly
            min_agreement = min(agreement_data)
            max_agreement = max(agreement_data)
            y_range = max_agreement - min_agreement
            ax2.set_ylim(min_agreement - y_range * 0.1, max_agreement + y_range * 0.15)

            # Add value labels with better positioning
            for bar, agreement in zip(bars, agreement_data):
                height = bar.get_height()
                ax2.text(bar.get_x() + bar.get_width()/2., height + y_range * 0.02,
                        f'{agreement:.1f}%', ha='center', va='bottom', fontweight='bold', fontsize=10)
        
        # Plot 3: Accuracy Difference from Idris (middle left)
        ax3 = plt.subplot(3, 2, 3)
        diff_data = []
        diff_labels = []
        
        for dataset, results in analysis_results.items():
            if 'accuracies' in results and 'idris' in results['accuracies']:
                idris_acc = results['accuracies']['idris']
                for impl, acc in results['accuracies'].items():
                    if impl != 'idris':
                        diff = acc - idris_acc
                        diff_data.append(diff)
                        diff_labels.append(f"{dataset.replace('_', ' ').title()}\n{impl.replace('_', ' ').title()}")
        
        if diff_data:
            colors_diff = ['green' if d >= 0 else 'red' for d in diff_data]
            bars = ax3.bar(range(len(diff_data)), diff_data, color=colors_diff, alpha=0.7)
            ax3.set_xlabel('Implementation', fontweight='bold')
            ax3.set_ylabel('Accuracy Difference from Idris (%)', fontweight='bold')
            ax3.set_title('Accuracy Gap: Other Implementations vs Idris2', fontweight='bold', fontsize=14)
            ax3.set_xticks(range(len(diff_data)))
            ax3.set_xticklabels(diff_labels, rotation=45, ha='right', fontsize=10)
            ax3.grid(True, alpha=0.3)
            ax3.axhline(y=0, color='black', linestyle='-', alpha=0.5)
            
            # Add value labels
            for bar, diff in zip(bars, diff_data):
                height = bar.get_height()
                ax3.text(bar.get_x() + bar.get_width()/2., height + (0.2 if height >= 0 else -0.5),
                        f'{diff:+.1f}%', ha='center', va='bottom' if height >= 0 else 'top', 
                        fontweight='bold', fontsize=10)
        
        # Plot 4: Discrepancy Analysis (middle right)
        ax4 = plt.subplot(3, 2, 4)
        discrepancy_counts = []
        discrepancy_percentages = []
        
        for dataset, results in analysis_results.items():
            if 'total_discrepancies' in results and 'test_samples' in results:
                count = results['total_discrepancies']
                total = results['test_samples']
                percentage = (count / total) * 100 if total > 0 else 0
                discrepancy_counts.append(count)
                discrepancy_percentages.append(percentage)
        
        if discrepancy_counts:
            bars = ax4.bar(datasets, discrepancy_counts, color='#FF6B6B', alpha=0.7)
            ax4.set_xlabel('Dataset', fontweight='bold')
            ax4.set_ylabel('Number of Discrepancies', fontweight='bold')
            ax4.set_title('Idris vs Reference Implementation Discrepancies', fontweight='bold', fontsize=14)
            ax4.grid(True, alpha=0.3)
            
            # Add value labels with percentages
            for bar, count, pct in zip(bars, discrepancy_counts, discrepancy_percentages):
                height = bar.get_height()
                ax4.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                        f'{count}\n({pct:.1f}%)', ha='center', va='bottom', fontweight='bold', fontsize=10)
        
        # Plot 5: Dataset Complexity vs Idris Performance (bottom left)
        ax5 = plt.subplot(3, 2, 5)
        test_samples = []
        idris_accuracies = []
        
        for dataset, results in analysis_results.items():
            if 'test_samples' in results and 'accuracies' in results and 'idris' in results['accuracies']:
                test_samples.append(results['test_samples'])
                idris_accuracies.append(results['accuracies']['idris'])
        
        if test_samples:
            ax5.scatter(test_samples, idris_accuracies, s=150, alpha=0.7, color='#FF6B6B')
            ax5.set_xlabel('Test Samples (Dataset Size)', fontweight='bold')
            ax5.set_ylabel('Idris2 Accuracy (%)', fontweight='bold')
            ax5.set_title('Dataset Complexity vs Idris2 Performance', fontweight='bold', fontsize=14)
            ax5.grid(True, alpha=0.3)
            
            # Add dataset labels
            for i, (size, acc) in enumerate(zip(test_samples, idris_accuracies)):
                ax5.annotate(datasets[i], (size, acc), xytext=(5, 5), 
                            textcoords='offset points', fontsize=11, fontweight='bold')
            
            # Add trend line
            if len(test_samples) > 1:
                z = np.polyfit(test_samples, idris_accuracies, 1)
                p = np.poly1d(z)
                ax5.plot(test_samples, p(test_samples), "r--", alpha=0.8, linewidth=2)
        
        # Plot 6: Implementation Summary Table (bottom right)
        ax6 = plt.subplot(3, 2, 6)
        ax6.axis('tight')
        ax6.axis('off')
        
        # Create summary table
        table_data = []
        table_data.append(['Dataset', 'Idris2', 'Reference', 'Manual', 'Sklearn', 'Idris vs Ref'])
        
        for dataset, results in analysis_results.items():
            if 'accuracies' in results:
                row = [dataset.replace('_', ' ').title()]
                acc = results['accuracies']
                row.append(f"{acc.get('idris', 0):.1f}%")
                row.append(f"{acc.get('reference', 0):.1f}%")
                row.append(f"{acc.get('python_manual', 0):.1f}%")
                row.append(f"{acc.get('python_sklearn', 0):.1f}%")
                
                # Agreement with reference
                if 'agreements' in results:
                    agreement = results['agreements'].get('idris_vs_reference', 0)
                    row.append(f"{agreement:.1f}%")
                else:
                    row.append("N/A")
                
                table_data.append(row)
        
        table = ax6.table(cellText=table_data[1:], colLabels=table_data[0], 
                         cellLoc='center', loc='center')
        table.auto_set_font_size(False)
        table.set_fontsize(10)
        table.scale(1.2, 1.5)
        
        # Style the table
        for i in range(len(table_data[0])):
            table[(0, i)].set_facecolor('#4ECDC4')
            table[(0, i)].set_text_props(weight='bold')
        
        ax6.set_title('Implementation Comparison Summary', fontweight='bold', fontsize=14, pad=20)
        
        plt.tight_layout(pad=3.0)
        plt.savefig(self.results_dir / 'idris_comprehensive_comparison.png', dpi=300, bbox_inches='tight')
        print(f"Saved Idris comprehensive comparison plot")
        plt.close()
    
    def generate_idris_report(self, analysis_results: Dict) -> None:
        """Generate comprehensive Idris comparison report."""
        print("\nGenerating Idris comparison report...")
        
        report_lines = [
            "# Idris2 KNN vs Python Implementations: Comprehensive Analysis",
            "=" * 70,
            "",
            "## Executive Summary",
            "",
            "This analysis compares the type-safe Idris2 KNN implementation with dependent types",
            "against Python implementations across datasets of varying complexity.",
            "",
            "## Key Findings",
            ""
        ]
        
        # Calculate overall statistics
        total_discrepancies = 0
        total_samples = 0
        idris_wins = 0
        python_wins = 0
        
        for dataset, results in analysis_results.items():
            if 'total_discrepancies' in results:
                total_discrepancies += results['total_discrepancies']
                total_samples += results['test_samples']
            
            if 'accuracies' in results and 'idris' in results['accuracies']:
                idris_acc = results['accuracies']['idris']
                ref_acc = results['accuracies'].get('reference', 0)
                if idris_acc > ref_acc:
                    idris_wins += 1
                elif ref_acc > idris_acc:
                    python_wins += 1
        
        report_lines.extend([
            f"### Overall Performance",
            f"- **Total test samples analyzed**: {total_samples}",
            f"- **Total discrepancies found**: {total_discrepancies} ({(total_discrepancies/total_samples)*100:.1f}%)",
            f"- **Datasets where Idris2 wins**: {idris_wins}",
            f"- **Datasets where Python wins**: {python_wins}",
            "",
            "## Dataset-by-Dataset Analysis",
            ""
        ])
        
        for dataset, results in analysis_results.items():
            if 'accuracies' not in results:
                continue
                
            report_lines.extend([
                f"### {dataset.replace('_', ' ').title()} Dataset",
                f"- **Test samples**: {results['test_samples']}",
                ""
            ])
            
            # Accuracy comparison
            acc = results['accuracies']
            report_lines.append("**Accuracy Results:**")
            for impl, accuracy in acc.items():
                report_lines.append(f"- {impl.replace('_', ' ').title()}: {accuracy:.1f}%")
            
            # Agreement analysis
            if 'agreements' in results:
                report_lines.append("\n**Agreement with Idris2:**")
                for pair, agreement in results['agreements'].items():
                    other_impl = pair.replace('idris_vs_', '').replace('_', ' ').title()
                    report_lines.append(f"- vs {other_impl}: {agreement:.1f}%")
            
            # Discrepancy analysis
            if 'total_discrepancies' in results:
                count = results['total_discrepancies']
                total = results['test_samples']
                percentage = (count / total) * 100 if total > 0 else 0
                report_lines.append(f"\n**Discrepancies**: {count}/{total} ({percentage:.1f}%)")
                
                if results['discrepancies']:
                    report_lines.append("\n**Sample Discrepancies:**")
                    for disc in results['discrepancies'][:5]:  # Show first 5
                        report_lines.append(
                            f"- Sample {disc['index']}: Idris={disc['idris_pred']}, "
                            f"Reference={disc['reference_pred']}, True={disc['true_label']}"
                        )
            
            report_lines.append("")
        
        report_lines.extend([
            "## Type Safety vs Performance Analysis",
            "",
            "### Idris2 Advantages ",
            "- **Compile-time guarantees**: Prevents array bounds errors and dimension mismatches",
            "- **Mathematical correctness**: Type system enforces k ≤ training_samples",
            "- **Zero runtime errors**: Dependent types catch bugs before execution",
            "- **Explicit algorithms**: Clear, mathematically precise implementations",
            "",
            "### Idris2 Challenges ",
            "- **Performance gaps**: Lower accuracy on complex datasets",
            "- **Algorithm differences**: Different tie-breaking and precision handling",
            "- **Library ecosystem**: Limited ML optimizations compared to Python",
            "",
            "### Python Advantages ",
            "- **Optimized libraries**: Highly tuned implementations (sklearn)",
            "- **Better accuracy**: Superior performance on large, complex datasets",
            "- **Mature ecosystem**: Extensive ML library support",
            "",
            "## Recommendations",
            "",
            "### Use Idris2 KNN when:",
            "1. **Safety is critical**: Runtime errors are unacceptable",
            "2. **Mathematical correctness**: Type-level guarantees are required",
            "3. **Educational purposes**: Learning dependent types and formal verification",
            "4. **Small-medium datasets**: Where performance gaps are minimal",
            "",
            "### Use Python implementations when:",
            "1. **Performance is critical**: Maximum accuracy is required",
            "2. **Large datasets**: Complex, high-dimensional data",
            "3. **Production systems**: Mature, optimized implementations needed",
            "4. **Rapid development**: Quick prototyping and iteration",
            "",
            "## Conclusion",
            "",
            "While Idris2 provides valuable compile-time safety guarantees, the analysis reveals",
            "significant performance gaps on larger, more complex datasets. The type safety benefits",
            "come at the cost of accuracy, particularly as dataset complexity increases.",
            "",
            "This trade-off between safety and performance highlights the importance of choosing",
            "the right tool for the specific requirements of each machine learning application."
        ])
        
        # Save report
        report_file = self.results_dir / "idris_comparison_report.md"
        with open(report_file, 'w') as f:
            f.write('\n'.join(report_lines))
        
        print(f"Saved Idris comparison report to {report_file}")
    
    def run_analysis(self) -> None:
        """Run comprehensive Idris comparison analysis."""
        print("Starting Idris2 KNN Comprehensive Analysis")
        print("=" * 60)
        
        analysis_results = {}
        for dataset in self.datasets:
            result = self.analyze_dataset(dataset)
            if result:
                analysis_results[dataset] = result
        
        if analysis_results:
            self.create_comprehensive_plots(analysis_results)
            self.generate_idris_report(analysis_results)
            
            print(f"\nIdris2 comprehensive analysis complete!")
            print(f"Results saved to {self.results_dir}/")
            print(f"   - idris_comprehensive_comparison.png")
            print(f"   - idris_comparison_report.md")
        else:
            print("Error: No analysis results available")

def main():
    parser = argparse.ArgumentParser(description="Idris2 KNN comprehensive analysis")
    parser.add_argument("--results-dir", default="../results", help="Results directory")
    
    args = parser.parse_args()
    
    analyzer = IdrisComparisonAnalyzer(args.results_dir)
    analyzer.run_analysis()

if __name__ == "__main__":
    main()
