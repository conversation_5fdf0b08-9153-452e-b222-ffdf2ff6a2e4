"""
Visualization and comparison script for KNN experiment results.
Creates plots comparing Idris and Python implementations.
TODO: add more plot types for better analysis
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import confusion_matrix, accuracy_score
from sklearn.decomposition import PCA
import os
import argparse

# Set style for professional plots
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

def load_predictions():
    """Load predictions from all implementations."""
    results = {}
    
    try:
        # Load test data for true labels
        test_df = pd.read_csv("../data/iris_test.csv")
        true_labels = test_df.iloc[:, -1].values
        results['true'] = true_labels
        
        # Load Idris predictions
        if os.path.exists("../results/idris_predictions.txt"):
            idris_pred = np.loadtxt("../results/idris_predictions.txt", dtype=int)
            results['idris'] = idris_pred
            print("Loaded Idris predictions")
        else:
            print("Warning: Idris predictions not found")

        # Load Python manual predictions
        if os.path.exists("../results/python_manual_predictions.txt"):
            python_manual = np.loadtxt("../results/python_manual_predictions.txt", dtype=int)
            results['python_manual'] = python_manual
            print("Loaded Python manual predictions")
        else:
            print("Warning: Python manual predictions not found")

        # Load Python sklearn predictions
        if os.path.exists("../results/python_sklearn_predictions.txt"):
            python_sklearn = np.loadtxt("../results/python_sklearn_predictions.txt", dtype=int)
            results['python_sklearn'] = python_sklearn
            print("Loaded Python sklearn predictions")
        else:
            print("Warning: Python sklearn predictions not found")
            
    except Exception as e:
        print(f"Error loading predictions: {e}")
        return None

    return results

def plot_accuracy_comparison(results):
    """Create accuracy comparison bar plot."""
    if 'true' not in results:
        print("Cannot plot accuracy: true labels not available")
        return
        
    fig, ax = plt.subplots(1, 1, figsize=(10, 6))
    
    implementations = []
    accuracies = []
    colors = ['#2E86AB', '#A23B72', '#F18F01']
    
    for i, (name, predictions) in enumerate(results.items()):
        if name == 'true':
            continue
        acc = accuracy_score(results['true'], predictions) * 100
        implementations.append(name.replace('_', ' ').title())
        accuracies.append(acc)
    
    bars = ax.bar(implementations, accuracies, color=colors[:len(implementations)])
    
    # Add value labels on bars
    for bar, acc in zip(bars, accuracies):
        height = bar.get_height()
        ax.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                f'{acc:.1f}%', ha='center', va='bottom', fontweight='bold')
    
    ax.set_ylabel('Accuracy (%)', fontsize=12)
    ax.set_title('KNN Classification Accuracy Comparison', fontsize=14, fontweight='bold')
    ax.set_ylim(0, 105)
    ax.grid(True, alpha=0.3)
    
    plt.xticks(rotation=45)
    plt.tight_layout()
    plt.savefig('../results/accuracy_comparison.png', dpi=300, bbox_inches='tight')
    print("Saved accuracy comparison plot")

def plot_confusion_matrices(results):
    """Create confusion matrix plots for each implementation."""
    if 'true' not in results:
        print("Cannot plot confusion matrices: true labels not available")
        return
        
    class_names = ['Setosa', 'Versicolor', 'Virginica']
    
    # Count implementations (excluding 'true')
    n_impl = len([k for k in results.keys() if k != 'true'])
    
    fig, axes = plt.subplots(1, n_impl, figsize=(5*n_impl, 4))
    if n_impl == 1:
        axes = [axes]
    
    plot_idx = 0
    for name, predictions in results.items():
        if name == 'true':
            continue

        cm = confusion_matrix(results['true'], predictions)

        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
                   xticklabels=class_names, yticklabels=class_names,
                   ax=axes[plot_idx], cbar=True)

        axes[plot_idx].set_title(f'{name.replace("_", " ").title()}', fontweight='bold')
        axes[plot_idx].set_ylabel('True Label')
        axes[plot_idx].set_xlabel('Predicted Label')

        plot_idx += 1
    
    plt.tight_layout()
    plt.savefig('../results/confusion_matrices.png', dpi=300, bbox_inches='tight')
    print("Saved confusion matrices plot")

def plot_agreement_matrix(results):
    """Create agreement matrix between different implementations."""
    implementations = [k for k in results.keys() if k != 'true']
    
    if len(implementations) < 2:
        print("Warning: Need at least 2 implementations for agreement matrix")
        return
        
    n_impl = len(implementations)
    agreement_matrix = np.zeros((n_impl, n_impl))
    
    for i, impl1 in enumerate(implementations):
        for j, impl2 in enumerate(implementations):
            if i == j:
                agreement_matrix[i, j] = 100.0  # Perfect agreement with self
            else:
                agreement = np.mean(results[impl1] == results[impl2]) * 100
                agreement_matrix[i, j] = agreement
    
    fig, ax = plt.subplots(1, 1, figsize=(8, 6))
    
    im = ax.imshow(agreement_matrix, cmap='RdYlGn', vmin=0, vmax=100)
    
    # Add labels
    impl_labels = [impl.replace('_', '\n').title() for impl in implementations]
    ax.set_xticks(range(n_impl))
    ax.set_yticks(range(n_impl))
    ax.set_xticklabels(impl_labels)
    ax.set_yticklabels(impl_labels)
    
    # Add text annotations
    for i in range(n_impl):
        for j in range(n_impl):
            text = ax.text(j, i, f'{agreement_matrix[i, j]:.1f}%',
                          ha='center', va='center', fontweight='bold')
    
    ax.set_title('Implementation Agreement Matrix', fontsize=14, fontweight='bold')
    
    # Add colorbar
    cbar = plt.colorbar(im, ax=ax)
    cbar.set_label('Agreement (%)', rotation=270, labelpad=20)
    
    plt.tight_layout()
    plt.savefig('../results/agreement_matrix.png', dpi=300, bbox_inches='tight')
    print("Saved agreement matrix plot")

def plot_data_visualization():
    """Create data visualization using PCA."""
    try:
        # Load full dataset
        full_df = pd.read_csv("../data/iris_full.csv")
        X = full_df.iloc[:, :-1].values
        y = full_df.iloc[:, -1].values
        
        # Apply PCA for 2D visualization
        pca = PCA(n_components=2)
        X_pca = pca.fit_transform(X)
        
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        # Original feature space (first 2 features)
        class_names = ['Setosa', 'Versicolor', 'Virginica']
        colors = ['#FF6B6B', '#4ECDC4', '#45B7D1']
        
        for i, (class_name, color) in enumerate(zip(class_names, colors)):
            mask = y == i
            ax1.scatter(X[mask, 0], X[mask, 1], c=color, label=class_name, alpha=0.7)
        
        ax1.set_xlabel('Sepal Length (standardized)')
        ax1.set_ylabel('Sepal Width (standardized)')
        ax1.set_title('Iris Dataset: Original Features', fontweight='bold')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # PCA space
        for i, (class_name, color) in enumerate(zip(class_names, colors)):
            mask = y == i
            ax2.scatter(X_pca[mask, 0], X_pca[mask, 1], c=color, label=class_name, alpha=0.7)
        
        ax2.set_xlabel(f'PC1 ({pca.explained_variance_ratio_[0]:.1%} variance)')
        ax2.set_ylabel(f'PC2 ({pca.explained_variance_ratio_[1]:.1%} variance)')
        ax2.set_title('Iris Dataset: PCA Space', fontweight='bold')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('../results/data_visualization.png', dpi=300, bbox_inches='tight')
        print("Saved data visualization plot")
        
    except Exception as e:
        print(f"Error creating data visualization: {e}")

def create_summary_report(results):
    """Create a summary report of the experiment."""
    if 'true' not in results:
        print("Cannot create summary: true labels not available")
        return
        
    report = []
    report.append("# KNN Classification Experiment Summary")
    report.append("=" * 50)
    report.append("")
    
    # Dataset info
    test_df = pd.read_csv("../data/iris_test.csv")
    train_df = pd.read_csv("../data/iris_train.csv")
    
    report.append("## Dataset Information")
    report.append(f"- Dataset: Iris flower classification")
    report.append(f"- Features: {test_df.shape[1] - 1} (sepal/petal length & width)")
    report.append(f"- Classes: 3 (Setosa, Versicolor, Virginica)")
    report.append(f"- Training samples: {len(train_df)}")
    report.append(f"- Test samples: {len(test_df)}")
    report.append("")
    
    # Results comparison
    report.append("## Results Comparison")
    report.append("| Implementation | Accuracy | Type Safety | Notes |")
    report.append("|----------------|----------|-------------|--------|")
    
    for name, predictions in results.items():
        if name == 'true':
            continue
        acc = accuracy_score(results['true'], predictions) * 100
        
        if 'idris' in name:
            safety = "Compile-time"
            notes = "Dependent types, shape checking"
        elif 'manual' in name:
            safety = "Warning: Runtime"
            notes = "Manual implementation"
        else:
            safety = "Warning: Runtime"
            notes = "Optimized library"
            
        report.append(f"| {name.replace('_', ' ').title()} | {acc:.1f}% | {safety} | {notes} |")
    
    report.append("")
    
    # Agreement analysis
    implementations = [k for k in results.keys() if k != 'true']
    if len(implementations) >= 2:
        report.append("## Implementation Agreement")
        for i, impl1 in enumerate(implementations):
            for impl2 in implementations[i+1:]:
                agreement = np.mean(results[impl1] == results[impl2]) * 100
                report.append(f"- {impl1.title()} vs {impl2.title()}: {agreement:.1f}% agreement")
        report.append("")
    
    # Key findings
    report.append("## Key Findings")
    report.append("- All implementations achieve similar accuracy on Iris dataset")
    report.append("- Idris provides compile-time guarantees for array shapes and bounds")
    report.append("- Type safety prevents common runtime errors (index out of bounds, dimension mismatches)")
    report.append("- Dependent types enable expressing algorithm constraints in the type system")
    
    # Save report
    os.makedirs("../results", exist_ok=True)
    with open("../results/experiment_summary.md", "w") as f:
        f.write("\n".join(report))
    
    print("Saved experiment summary report")

def main():
    parser = argparse.ArgumentParser(description="Create comparison plots for KNN experiment")
    parser.add_argument("--all", action="store_true", 
                       help="Generate all plots and reports")
    parser.add_argument("--accuracy", action="store_true",
                       help="Generate accuracy comparison plot")
    parser.add_argument("--confusion", action="store_true",
                       help="Generate confusion matrices")
    parser.add_argument("--agreement", action="store_true",
                       help="Generate agreement matrix")
    parser.add_argument("--data-viz", action="store_true",
                       help="Generate data visualization")
    parser.add_argument("--report", action="store_true",
                       help="Generate summary report")
    
    args = parser.parse_args()
    
    print("KNN Experiment Analysis & Visualization")
    print("="*50)
    
    # Create results directory
    os.makedirs("../results", exist_ok=True)
    
    # Load all predictions
    print("\nLoading prediction results...")
    results = load_predictions()
    
    if results is None or len(results) <= 1:
        print("Insufficient data for comparison plots")
        return
    
    print(f"Loaded results from {len(results)-1} implementations")
    
    # Generate requested plots
    if args.all or args.accuracy:
        print("\nCreating accuracy comparison...")
        plot_accuracy_comparison(results)
    
    if args.all or args.confusion:
        print("\nCreating confusion matrices...")
        plot_confusion_matrices(results)
    
    if args.all or args.agreement:
        print("\nCreating agreement matrix...")
        plot_agreement_matrix(results)
    
    if args.all or args.data_viz:
        print("\nCreating data visualization...")
        plot_data_visualization()
    
    if args.all or args.report:
        print("\nCreating summary report...")
        create_summary_report(results)
    
    if not any([args.all, args.accuracy, args.confusion, args.agreement, args.data_viz, args.report]):
        print("\nNo specific plots requested. Use --all to generate everything.")
        print("   Available options: --accuracy, --confusion, --agreement, --data-viz, --report")
    
    print(f"\nAnalysis complete! Check ../results/ for output files.")

if __name__ == "__main__":
    main()