"""
Progressive data preprocessing for KNN experiment.
Creates datasets with specific sizes: Iris (100), Wine (150), Breast Cancer (500), Synthetic (5000).
Uses 80/20 train/test split for all datasets.

TODO: add more sophisticated synthetic data generation
FIXME: might want to validate synthetic data quality
"""

import numpy as np
import pandas as pd
import os
from sklearn.datasets import load_iris, load_wine, load_breast_cancer, make_classification
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.utils import resample
import argparse
from pathlib import Path
# import matplotlib.pyplot as plt  # for data visualization if needed

class ProgressiveDataPreprocessor:
    """Handles preprocessing with specific dataset sizes and 80/20 split."""
    
    def __init__(self, test_size=0.2, random_state=42):
        self.test_size = test_size
        self.random_state = random_state
        self.data_dir = Path("../data")
        self.datasets_info = {}
        
        # Target dataset sizes
        self.target_sizes = {
            'iris': 100,
            'wine': 150, 
            'breast_cancer': 500,
            'synthetic': 5000
        }
        
        # Create data directory
        self.data_dir.mkdir(exist_ok=True)
        print("Data directory created/verified")
        
    def load_and_resize_dataset(self, dataset_name):
        """Load dataset and resize to target size using resampling."""
        target_size = self.target_sizes[dataset_name]
        
        if dataset_name == 'iris':
            data = load_iris()
            print(f"Original Iris: {data.data.shape[0]} samples -> Target: {target_size}")
        elif dataset_name == 'wine':
            data = load_wine()
            print(f"Original Wine: {data.data.shape[0]} samples -> Target: {target_size}")
        elif dataset_name == 'breast_cancer':
            data = load_breast_cancer()
            print(f"Original Breast Cancer: {data.data.shape[0]} samples -> Target: {target_size}")
        elif dataset_name == 'synthetic':
            return self.generate_synthetic_dataset()
        else:
            raise ValueError(f"Unknown dataset: {dataset_name}")
            
        X, y = data.data, data.target
        feature_names = data.feature_names
        target_names = data.target_names
        
        # Resize dataset using stratified resampling
        if len(X) < target_size:
            # Upsample if original is smaller
            print(f"Upsampling from {len(X)} to {target_size} samples")
            X_resampled, y_resampled = resample(
                X, y, 
                n_samples=target_size, 
                random_state=self.random_state,
                stratify=y
            )
        elif len(X) > target_size:
            # Downsample if original is larger
            print(f"Downsampling from {len(X)} to {target_size} samples")
            X_resampled, y_resampled = resample(
                X, y, 
                n_samples=target_size, 
                random_state=self.random_state,
                stratify=y
            )
        else:
            # Keep original size
            X_resampled, y_resampled = X, y
            
        return X_resampled, y_resampled, feature_names, target_names
    
    def generate_synthetic_dataset(self):
        """Generate synthetic dataset with 5000 samples."""
        print("Generating synthetic dataset with 5000 samples...")
        
        # Create synthetic classification dataset
        X, y = make_classification(
            n_samples=5000,
            n_features=12,
            n_informative=8,
            n_redundant=2,
            n_clusters_per_class=2,
            n_classes=4,
            random_state=self.random_state,
            class_sep=1.2
        )
        
        # Create feature names
        feature_names = [f'feature_{i}' for i in range(12)]
        target_names = [f'class_{i}' for i in range(4)]
        
        print(f"Generated synthetic dataset: {X.shape[0]} samples, {X.shape[1]} features, {len(target_names)} classes")
        
        return X, y, feature_names, target_names
    
    def preprocess_dataset(self, dataset_name):
        """Preprocess a single dataset with controlled parameters."""
        print(f"\nProcessing {dataset_name} dataset...")
        
        # Load and resize data
        X, y, feature_names, target_names = self.load_and_resize_dataset(dataset_name)
        
        # Split data with 80/20 train/test split
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, 
            test_size=self.test_size, 
            random_state=self.random_state, 
            stratify=y
        )
        
        train_size = len(X_train)
        test_size = len(X_test)
        print(f"Split: {train_size} train ({train_size/(train_size+test_size)*100:.0f}%), {test_size} test ({test_size/(train_size+test_size)*100:.0f}%)")
        
        # Standardize features - critical for KNN performance
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        X_test_scaled = scaler.transform(X_test)
        
        # Also scale full dataset for completeness
        X_full_scaled = scaler.fit_transform(X)
        
        print(f"Features standardized (mean≈0, std≈1)")
        
        # Store dataset info for validation
        self.datasets_info[dataset_name] = {
            'n_samples': len(X),
            'n_features': X.shape[1],
            'n_classes': len(target_names),
            'n_train': len(X_train),
            'n_test': len(X_test),
            'feature_names': list(feature_names),
            'target_names': list(target_names),
            'train_test_ratio': f"{int((1-self.test_size)*100)}/{int(self.test_size*100)}"
        }
        
        return X_train_scaled, X_test_scaled, y_train, y_test, X_full_scaled, y, feature_names
    
    def save_dataset(self, dataset_name, X_train, X_test, y_train, y_test, X_full, y_full, feature_names):
        """Save processed dataset to CSV files."""
        print(f"Saving {dataset_name} dataset files...")
        
        # Create DataFrames with proper column names
        train_df = pd.DataFrame(X_train, columns=feature_names)
        train_df['target'] = y_train
        
        test_df = pd.DataFrame(X_test, columns=feature_names)
        test_df['target'] = y_test
        
        full_df = pd.DataFrame(X_full, columns=feature_names)
        full_df['target'] = y_full
        
        # Save to CSV files with consistent naming
        train_file = self.data_dir / f"{dataset_name}_train.csv"
        test_file = self.data_dir / f"{dataset_name}_test.csv"
        full_file = self.data_dir / f"{dataset_name}_full.csv"
        
        train_df.to_csv(train_file, index=False, float_format='%.6f')
        test_df.to_csv(test_file, index=False, float_format='%.6f')
        full_df.to_csv(full_file, index=False, float_format='%.6f')
        
        print(f"  Saved: {train_file} ({len(train_df)} samples)")
        print(f"  Saved: {test_file} ({len(test_df)} samples)")
        print(f"  Saved: {full_file} ({len(full_df)} samples)")
        
    def validate_data_consistency(self):
        """Validate that all datasets are properly processed."""
        print("\nValidating data consistency...")
        
        for dataset_name in ['iris', 'wine', 'breast_cancer', 'synthetic']:
            info = self.datasets_info[dataset_name]
            
            # Check files exist
            train_file = self.data_dir / f"{dataset_name}_train.csv"
            test_file = self.data_dir / f"{dataset_name}_test.csv"
            full_file = self.data_dir / f"{dataset_name}_full.csv"
            
            if not all([train_file.exists(), test_file.exists(), full_file.exists()]):
                print(f"Warning: Missing files for {dataset_name}")
                continue
                
            # Verify file contents
            train_df = pd.read_csv(train_file)
            test_df = pd.read_csv(test_file)
            full_df = pd.read_csv(full_file)
            
            # Check dimensions
            expected_train = info['n_train']
            expected_test = info['n_test']
            expected_features = info['n_features']
            
            if len(train_df) != expected_train:
                print(f"Warning: {dataset_name} train size mismatch")
            if len(test_df) != expected_test:
                print(f"Warning: {dataset_name} test size mismatch")
            if train_df.shape[1] != expected_features + 1:  # +1 for target
                print(f"Warning: {dataset_name} feature count mismatch")
                
            print(f"  {dataset_name}: {len(train_df)} train, {len(test_df)} test, {train_df.shape[1]-1} features - OK")
    
    def print_summary(self):
        """Print summary of all processed datasets."""
        print("\n" + "="*70)
        print("PROGRESSIVE DATASET PREPROCESSING SUMMARY")
        print("="*70)
        
        total_train = 0
        total_test = 0
        
        for dataset_name, info in self.datasets_info.items():
            print(f"\n{dataset_name.upper()}:")
            print(f"  Total samples: {info['n_samples']}")
            print(f"  Features: {info['n_features']}")
            print(f"  Classes: {info['n_classes']}")
            print(f"  Train samples: {info['n_train']}")
            print(f"  Test samples: {info['n_test']}")
            print(f"  Split ratio: {info['train_test_ratio']}")
            
            total_train += info['n_train']
            total_test += info['n_test']
        
        print(f"\nTOTAL ACROSS ALL DATASETS:")
        print(f"  Train samples: {total_train}")
        print(f"  Test samples: {total_test}")
        print(f"  Total samples: {total_train + total_test}")
        
        print(f"\nParameters used:")
        print(f"  Test size: {self.test_size} (80/20 split)")
        print(f"  Random state: {self.random_state}")
        print(f"  Scaling: StandardScaler (mean=0, std=1)")
        print(f"  Resampling: Stratified for class balance")
        
    def process_all_datasets(self):
        """Process all four datasets with progressive sizes."""
        datasets = ['iris', 'wine', 'breast_cancer', 'synthetic']
        
        print("Starting progressive data preprocessing...")
        print(f"Target sizes: Iris={self.target_sizes['iris']}, Wine={self.target_sizes['wine']}, "
              f"Breast Cancer={self.target_sizes['breast_cancer']}, Synthetic={self.target_sizes['synthetic']}")
        print(f"Train/Test split: {int((1-self.test_size)*100)}/{int(self.test_size*100)}")
        print(f"Random state: {self.random_state}")
        
        for dataset_name in datasets:
            X_train, X_test, y_train, y_test, X_full, y_full, feature_names = self.preprocess_dataset(dataset_name)
            self.save_dataset(dataset_name, X_train, X_test, y_train, y_test, X_full, y_full, feature_names)
        
        # Validate everything
        self.validate_data_consistency()
        self.print_summary()
        
        print(f"\nProgressive data preprocessing completed!")
        print("All models will now receive identical train/test splits with controlled sizes")

def main():
    parser = argparse.ArgumentParser(description="Progressive data preprocessing for KNN experiment")
    parser.add_argument("--test-size", type=float, default=0.2, 
                       help="Proportion of data for testing (default: 0.2 for 80/20 split)")
    parser.add_argument("--random-seed", type=int, default=42,
                       help="Random seed for reproducibility (default: 42)")
    
    args = parser.parse_args()
    
    # Create preprocessor and process all datasets
    preprocessor = ProgressiveDataPreprocessor(
        test_size=args.test_size,
        random_state=args.random_seed
    )
    
    preprocessor.process_all_datasets()

if __name__ == "__main__":
    main()
