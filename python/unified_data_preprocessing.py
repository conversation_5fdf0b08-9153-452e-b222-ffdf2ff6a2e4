"""
Unified data preprocessing for KNN experiment.
Processes Iris, Wine, and Breast Cancer datasets with controlled splits.
Ensures all models receive identical training and test data.

TODO: add validation to ensure data consistency
FIXME: might want to add more sophisticated scaling options (done)
"""

import numpy as np
import pandas as pd
import os
from sklearn.datasets import load_iris, load_wine, load_breast_cancer, make_classification
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.utils import resample
import argparse
from pathlib import Path
# import json  # might need for saving metadata later

class UnifiedDataPreprocessor:
    """Handles preprocessing for all three datasets with controlled parameters."""
    
    def __init__(self, test_size=0.4, random_state=42):
        self.test_size = test_size
        self.random_state = random_state
        self.data_dir = Path("../data")
        self.datasets_info = {}
        
        # Create data directory
        self.data_dir.mkdir(exist_ok=True)
        print("Data directory created/verified")
        
    def load_dataset(self, dataset_name):
        """Load a specific dataset from sklearn."""
        if dataset_name == 'iris':
            data = load_iris()
            print(f"Loading Iris dataset: {data.data.shape[0]} samples, {data.data.shape[1]} features, {len(data.target_names)} classes")
        elif dataset_name == 'wine':
            data = load_wine()
            print(f"Loading Wine dataset: {data.data.shape[0]} samples, {data.data.shape[1]} features, {len(data.target_names)} classes")
        elif dataset_name == 'breast_cancer':
            data = load_breast_cancer()
            print(f"Loading Breast Cancer dataset: {data.data.shape[0]} samples, {data.data.shape[1]} features, {len(data.target_names)} classes")
        else:
            raise ValueError(f"Unknown dataset: {dataset_name}")
            
        return data.data, data.target, data.feature_names, data.target_names
    
    def preprocess_dataset(self, dataset_name):
        """Preprocess a single dataset with controlled parameters."""
        print(f"\nProcessing {dataset_name} dataset...")
        
        # Load raw data
        X, y, feature_names, target_names = self.load_dataset(dataset_name)
        
        # Split data with controlled random state
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, 
            test_size=self.test_size, 
            random_state=self.random_state, 
            stratify=y
        )
        
        print(f"Split: {len(X_train)} train, {len(X_test)} test samples")
        
        # Standardize features - important for KNN
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        X_test_scaled = scaler.transform(X_test)
        
        # Also scale full dataset for completeness
        X_full_scaled = scaler.fit_transform(X)
        
        print(f"Features standardized (mean≈0, std≈1)")
        
        # Store dataset info for validation
        self.datasets_info[dataset_name] = {
            'n_samples': len(X),
            'n_features': X.shape[1],
            'n_classes': len(target_names),
            'n_train': len(X_train),
            'n_test': len(X_test),
            'feature_names': list(feature_names),
            'target_names': list(target_names)
        }
        
        return X_train_scaled, X_test_scaled, y_train, y_test, X_full_scaled, y, feature_names
    
    def save_dataset(self, dataset_name, X_train, X_test, y_train, y_test, X_full, y_full, feature_names):
        """Save processed dataset to CSV files."""
        print(f"Saving {dataset_name} dataset files...")
        
        # Create DataFrames with proper column names
        train_df = pd.DataFrame(X_train, columns=feature_names)
        train_df['target'] = y_train
        
        test_df = pd.DataFrame(X_test, columns=feature_names)
        test_df['target'] = y_test
        
        full_df = pd.DataFrame(X_full, columns=feature_names)
        full_df['target'] = y_full
        
        # Save to CSV files with consistent naming
        train_file = self.data_dir / f"{dataset_name}_train.csv"
        test_file = self.data_dir / f"{dataset_name}_test.csv"
        full_file = self.data_dir / f"{dataset_name}_full.csv"
        
        train_df.to_csv(train_file, index=False, float_format='%.6f')
        test_df.to_csv(test_file, index=False, float_format='%.6f')
        full_df.to_csv(full_file, index=False, float_format='%.6f')
        
        print(f"  Saved: {train_file} ({len(train_df)} samples)")
        print(f"  Saved: {test_file} ({len(test_df)} samples)")
        print(f"  Saved: {full_file} ({len(full_df)} samples)")
        
    def validate_data_consistency(self):
        """Validate that all datasets are properly processed."""
        print("\nValidating data consistency...")
        
        for dataset_name in ['iris', 'wine', 'breast_cancer']:
            info = self.datasets_info[dataset_name]
            
            # Check files exist
            train_file = self.data_dir / f"{dataset_name}_train.csv"
            test_file = self.data_dir / f"{dataset_name}_test.csv"
            full_file = self.data_dir / f"{dataset_name}_full.csv"
            
            if not all([train_file.exists(), test_file.exists(), full_file.exists()]):
                print(f"Warning: Missing files for {dataset_name}")
                continue
                
            # Verify file contents
            train_df = pd.read_csv(train_file)
            test_df = pd.read_csv(test_file)
            full_df = pd.read_csv(full_file)
            
            # Check dimensions
            expected_train = info['n_train']
            expected_test = info['n_test']
            expected_features = info['n_features']
            
            if len(train_df) != expected_train:
                print(f"Warning: {dataset_name} train size mismatch")
            if len(test_df) != expected_test:
                print(f"Warning: {dataset_name} test size mismatch")
            if train_df.shape[1] != expected_features + 1:  # +1 for target
                print(f"Warning: {dataset_name} feature count mismatch")
                
            print(f"  {dataset_name}: {len(train_df)} train, {len(test_df)} test, {train_df.shape[1]-1} features - OK")
    
    def print_summary(self):
        """Print summary of all processed datasets."""
        print("\n" + "="*60)
        print("DATASET PREPROCESSING SUMMARY")
        print("="*60)
        
        total_train = 0
        total_test = 0
        
        for dataset_name, info in self.datasets_info.items():
            print(f"\n{dataset_name.upper()}:")
            print(f"  Total samples: {info['n_samples']}")
            print(f"  Features: {info['n_features']}")
            print(f"  Classes: {info['n_classes']}")
            print(f"  Train samples: {info['n_train']}")
            print(f"  Test samples: {info['n_test']}")
            print(f"  Test ratio: {info['n_test']/info['n_samples']:.1%}")
            
            total_train += info['n_train']
            total_test += info['n_test']
        
        print(f"\nTOTAL ACROSS ALL DATASETS:")
        print(f"  Train samples: {total_train}")
        print(f"  Test samples: {total_test}")
        print(f"  Total samples: {total_train + total_test}")
        
        print(f"\nParameters used:")
        print(f"  Test size: {self.test_size}")
        print(f"  Random state: {self.random_state}")
        print(f"  Scaling: StandardScaler (mean=0, std=1)")
        
    def process_all_datasets(self):
        """Process all three datasets."""
        datasets = ['iris', 'wine', 'breast_cancer']
        
        print("Starting unified data preprocessing...")
        print(f"Target datasets: {datasets}")
        print(f"Test size: {self.test_size}, Random state: {self.random_state}")
        
        for dataset_name in datasets:
            X_train, X_test, y_train, y_test, X_full, y_full, feature_names = self.preprocess_dataset(dataset_name)
            self.save_dataset(dataset_name, X_train, X_test, y_train, y_test, X_full, y_full, feature_names)
        
        # Validate everything
        self.validate_data_consistency()
        self.print_summary()
        
        print(f"\nData preprocessing completed!")
        print("All models will now receive identical train/test splits")

def main():
    parser = argparse.ArgumentParser(description="Unified data preprocessing for KNN experiment")
    parser.add_argument("--test-size", type=float, default=0.4, 
                       help="Proportion of data for testing (default: 0.4)")
    parser.add_argument("--random-seed", type=int, default=42,
                       help="Random seed for reproducibility (default: 42)")
    
    args = parser.parse_args()
    
    # Create preprocessor and process all datasets
    preprocessor = UnifiedDataPreprocessor(
        test_size=args.test_size,
        random_state=args.random_seed
    )
    
    preprocessor.process_all_datasets()

if __name__ == "__main__":
    main()
