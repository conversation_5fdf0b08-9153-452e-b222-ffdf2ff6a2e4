"""
Controlled experiment runner to ensure all implementations use identical datasets.
FIXME: should probably add more error handling for subprocess calls
"""

import subprocess
import pandas as pd
import numpy as np
from pathlib import Path
import time
# import json  # might need this for config files later

class ControlledExperimentRunner:
    """Run controlled experiments with dataset validation."""
    
    def __init__(self):
        # NOTE: progressive dataset sizes with synthetic data for scalability testing
        self.datasets = ['iris', 'wine', 'breast_cancer', 'synthetic']
        self.results_dir = Path("../results")
        self.data_dir = Path("../data")
        
    def validate_dataset(self, dataset_name: str) -> dict:
        """Validate a dataset and return its properties."""
        test_file = self.data_dir / f"{dataset_name}_test.csv"
        train_file = self.data_dir / f"{dataset_name}_train.csv"
        
        if not test_file.exists() or not train_file.exists():
            return {"valid": False, "error": "Files not found"}
        
        try:
            test_df = pd.read_csv(test_file)
            train_df = pd.read_csv(train_file)
            
            return {
                "valid": True,
                "test_samples": len(test_df),
                "train_samples": len(train_df),
                "features": test_df.shape[1] - 1,  # Exclude target column
                "classes": len(test_df.iloc[:, -1].unique())
            }
        except Exception as e:
            return {"valid": False, "error": str(e)}
    
    def run_reference_implementation(self, dataset_name: str) -> bool:
        """Run reference implementation."""
        print(f"  Running reference implementation...")
        try:
            result = subprocess.run([
                "python3", "knn_reference.py", "--dataset", dataset_name
            ], capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                print(f"    Reference completed successfully")
                return True
            else:
                print(f"    Reference failed: {result.stderr}")
                return False
        except Exception as e:
            print(f"    Reference error: {e}")
            return False
    
    def run_python_implementation(self, dataset_name: str) -> bool:
        """Run Python manual and sklearn implementations."""
        print(f"  🐍 Running Python implementations...")
        try:
            result = subprocess.run([
                "python3", "knn_python.py", "--dataset", dataset_name
            ], capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                print(f"    Python implementations completed successfully")
                return True
            else:
                print(f"    Python implementations failed: {result.stderr}")
                return False
        except Exception as e:
            print(f"    Python implementations error: {e}")
            return False
    
    def run_idris_implementation(self, dataset_name: str) -> bool:
        """Run Idris implementation for large test datasets."""
        print(f"  Running Idris implementation...")
        try:
            # Change to project root directory for Idris execution
            project_root = Path("..").resolve()

            # Map dataset names to Idris arguments
            dataset_mapping = {
                'iris': 'iris',
                'wine': 'wine',
                'breast_cancer': 'breast_cancer',
                'synthetic': 'synthetic'
            }

            if dataset_name not in dataset_mapping:
                print(f"    Unknown dataset: {dataset_name}")
                return False

            idris_arg = dataset_mapping[dataset_name]
            result = subprocess.run([
                "./build/exec/knn-configurable", idris_arg
            ], capture_output=True, text=True, timeout=300, cwd=project_root)

            if result.returncode == 0:
                print(f"    Idris implementation completed successfully")

                # Copy results to standardized names
                source_file = f"results/{idris_arg}_predictions.txt"
                target_file = f"results/idris_{dataset_name}_predictions.txt"
                subprocess.run(["cp", source_file, target_file], cwd=project_root)

                return True
            else:
                print(f"    Idris implementation failed: {result.stderr}")
                return False
        except Exception as e:
            print(f"    Idris implementation error: {e}")
            return False
    
    def validate_results(self, dataset_name: str, expected_samples: int) -> dict:
        """Validate that all implementations produced the same number of predictions."""
        print(f"  Validating results...")
        
        implementations = {
            'idris': f"idris_{dataset_name}_predictions.txt",
            'reference': f"reference_{dataset_name}_predictions.txt",
            'python_manual': f"python_manual_{dataset_name}_predictions.txt",
            'python_sklearn': f"python_sklearn_{dataset_name}_predictions.txt"
        }
        
        results = {}
        all_consistent = True
        
        for impl, filename in implementations.items():
            filepath = self.results_dir / filename
            if filepath.exists():
                try:
                    predictions = np.loadtxt(filepath, dtype=int)
                    count = len(predictions)
                    results[impl] = count
                    
                    status = "" if count == expected_samples else ""
                    print(f"    {status} {impl:15}: {count:3d} predictions")
                    
                    if count != expected_samples:
                        all_consistent = False
                        
                except Exception as e:
                    print(f"    {impl:15}: Error loading - {e}")
                    all_consistent = False
            else:
                print(f"    Warning: {impl:15}: File not found")
                all_consistent = False
        
        return {
            "consistent": all_consistent,
            "results": results,
            "expected": expected_samples
        }
    
    def run_controlled_experiment(self, dataset_name: str) -> bool:
        """Run a complete controlled experiment for one dataset."""
        print(f"\nCONTROLLED EXPERIMENT: {dataset_name.upper()}")
        print("=" * 60)
        
        # Validate dataset
        dataset_info = self.validate_dataset(dataset_name)
        if not dataset_info["valid"]:
            print(f"Dataset validation failed: {dataset_info['error']}")
            return False
        
        print(f"Dataset info: {dataset_info['test_samples']} test samples, "
              f"{dataset_info['features']} features, {dataset_info['classes']} classes")
        
        # Run all implementations
        success_count = 0
        
        if self.run_reference_implementation(dataset_name):
            success_count += 1
        
        if self.run_python_implementation(dataset_name):
            success_count += 1
            
        if self.run_idris_implementation(dataset_name):
            success_count += 1
        
        # Validate results
        validation = self.validate_results(dataset_name, dataset_info["test_samples"])
        
        if validation["consistent"]:
            print(f"  All implementations consistent: {dataset_info['test_samples']} predictions")
            return True
        else:
            print(f" WARNING: INCONSISTENT RESULTS!")
            print(f"     Expected: {validation['expected']} predictions")
            print(f"     Actual: {validation['results']}")
            return False
    
    def run_all_experiments(self):
        """Run controlled experiments for all datasets."""
        print("CONTROLLED EXPERIMENT SUITE")
        print("=" * 70)
        print("Ensuring all implementations use identical datasets...")
        
        results = {}
        
        for dataset in self.datasets:
            success = self.run_controlled_experiment(dataset)
            results[dataset] = success
            
            if not success:
                print(f"\nWarning: {dataset} experiment had issues - continuing with others...")
        
        # Summary
        print(f"\nEXPERIMENT SUMMARY")
        print("=" * 30)
        
        successful = sum(results.values())
        total = len(results)
        
        for dataset, success in results.items():
            status = "PASS" if success else "FAIL"
            print(f"{dataset:20} {status}")
        
        print(f"\nOverall: {successful}/{total} experiments successful")
        
        if successful == total:
            print(" All experiments completed with consistent datasets!")
            return True
        else:
            print(" Some experiments had dataset consistency issues!")
            return False

def main():
    runner = ControlledExperimentRunner()
    success = runner.run_all_experiments()
    
    if success:
        print("\nReady for scientific analysis with controlled datasets!")
    else:
        print("\nDataset consistency issues need to be resolved!")
        print("   Check the Idris CSV parsing implementation.")

if __name__ == "__main__":
    main()
