"""
Generate datasets with significantly larger test sets for better statistical analysis.
Maintains same total samples but increases test proportion for more robust evaluation.
TODO: add more dataset types for comprehensive testing
"""

import numpy as np
import pandas as pd
import os
from sklearn.datasets import load_iris, load_wine, load_breast_cancer, make_classification
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
import argparse

def create_data_directory():
    """Create data directory if it doesn't exist."""
    os.makedirs("../data", exist_ok=True)
    print(" Data directory created/verified")

def generate_iris_large_test(test_size=0.6, random_state=42):
    """Generate Iris dataset with larger test set (150 samples, 4 features, 3 classes)."""
    print("Generating Iris dataset with large test set...")
    
    iris = load_iris()
    X, y = iris.data, iris.target
    
    # Split with larger test set
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=test_size, random_state=random_state, stratify=y
    )
    
    # Standardize features
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_test_scaled = scaler.transform(X_test)
    
    print(f" Iris dataset: {len(X)} samples, {X.shape[1]} features, {len(np.unique(y))} classes")
    print(f"Split: {len(X_train)} train, {len(X_test)} test")
    
    # Save datasets
    train_df = pd.DataFrame(X_train_scaled, columns=iris.feature_names)
    train_df['target'] = y_train
    train_df.to_csv("../data/iris_large_test_train.csv", index=False)
    print(f" Saved iris training data: ../data/iris_large_test_train.csv ({len(X_train)} samples)")
    
    test_df = pd.DataFrame(X_test_scaled, columns=iris.feature_names)
    test_df['target'] = y_test
    test_df.to_csv("../data/iris_large_test_test.csv", index=False)
    print(f" Saved iris test data: ../data/iris_large_test_test.csv ({len(X_test)} samples)")
    
    # Save full dataset
    full_df = pd.DataFrame(scaler.fit_transform(X), columns=iris.feature_names)
    full_df['target'] = y
    full_df.to_csv("../data/iris_large_test_full.csv", index=False)
    print(f"Saved iris full data: ../data/iris_large_test_full.csv ({len(X)} samples)")
    
    return len(X_train), len(X_test), X.shape[1], len(np.unique(y))

def generate_wine_large_test(test_size=0.6, random_state=42):
    """Generate Wine dataset with larger test set (178 samples, 13 features, 3 classes)."""
    print("Generating Wine dataset with large test set...")
    
    wine = load_wine()
    X, y = wine.data, wine.target
    
    # Split with larger test set
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=test_size, random_state=random_state, stratify=y
    )
    
    # Standardize features
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_test_scaled = scaler.transform(X_test)
    
    print(f" Wine dataset: {len(X)} samples, {X.shape[1]} features, {len(np.unique(y))} classes")
    print(f" Split: {len(X_train)} train, {len(X_test)} test")
    
    # Save datasets
    train_df = pd.DataFrame(X_train_scaled, columns=wine.feature_names)
    train_df['target'] = y_train
    train_df.to_csv("../data/wine_large_test_train.csv", index=False)
    print(f" Saved wine training data: ../data/wine_large_test_train.csv ({len(X_train)} samples)")
    
    test_df = pd.DataFrame(X_test_scaled, columns=wine.feature_names)
    test_df['target'] = y_test
    test_df.to_csv("../data/wine_large_test_test.csv", index=False)
    print(f" Saved wine test data: ../data/wine_large_test_test.csv ({len(X_test)} samples)")
    
    # Save full dataset
    full_df = pd.DataFrame(scaler.fit_transform(X), columns=wine.feature_names)
    full_df['target'] = y
    full_df.to_csv("../data/wine_large_test_full.csv", index=False)
    print(f" Saved wine full data: ../data/wine_large_test_full.csv ({len(X)} samples)")
    
    return len(X_train), len(X_test), X.shape[1], len(np.unique(y))

def generate_breast_cancer_large_test(test_size=0.6, random_state=42):
    """Generate Breast Cancer dataset with larger test set (569 samples, 30 features, 2 classes)."""
    print("Generating Breast Cancer dataset with large test set...")
    
    cancer = load_breast_cancer()
    X, y = cancer.data, cancer.target
    
    # Split with larger test set
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=test_size, random_state=random_state, stratify=y
    )
    
    # Standardize features
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_test_scaled = scaler.transform(X_test)
    
    print(f" Breast Cancer dataset: {len(X)} samples, {X.shape[1]} features, {len(np.unique(y))} classes")
    print(f" Split: {len(X_train)} train, {len(X_test)} test")
    
    # Save datasets
    train_df = pd.DataFrame(X_train_scaled, columns=cancer.feature_names)
    train_df['target'] = y_train
    train_df.to_csv("../data/breast_cancer_large_test_train.csv", index=False)
    print(f" Saved breast_cancer training data: ../data/breast_cancer_large_test_train.csv ({len(X_train)} samples)")
    
    test_df = pd.DataFrame(X_test_scaled, columns=cancer.feature_names)
    test_df['target'] = y_test
    test_df.to_csv("../data/breast_cancer_large_test_test.csv", index=False)
    print(f" Saved breast_cancer test data: ../data/breast_cancer_large_test_test.csv ({len(X_test)} samples)")
    
    # Save full dataset
    full_df = pd.DataFrame(scaler.fit_transform(X), columns=cancer.feature_names)
    full_df['target'] = y
    full_df.to_csv("../data/breast_cancer_large_test_full.csv", index=False)
    print(f" Saved breast_cancer full data: ../data/breast_cancer_large_test_full.csv ({len(X)} samples)")
    
    return len(X_train), len(X_test), X.shape[1], len(np.unique(y))

def generate_synthetic_large_test(n_samples=5000, n_features=12, n_classes=5, test_size=0.7, random_state=42):
    """Generate synthetic dataset with very large test set."""
    print(f"Generating synthetic dataset with large test set ({n_samples} samples, {n_features} features, {n_classes} classes)...")
    
    X, y = make_classification(
        n_samples=n_samples,
        n_features=n_features,
        n_informative=n_features-2,
        n_redundant=2,
        n_classes=n_classes,
        n_clusters_per_class=1,
        random_state=random_state
    )
    
    # Split with very large test set
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=test_size, random_state=random_state, stratify=y
    )
    
    # Standardize features
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_test_scaled = scaler.transform(X_test)
    
    print(f"Synthetic dataset: {len(X)} samples, {X.shape[1]} features, {len(np.unique(y))} classes")
    print(f"Split: {len(X_train)} train, {len(X_test)} test")
    
    # Create feature names
    feature_names = [f'feature_{i}' for i in range(n_features)]
    
    # Save datasets
    dataset_name = f"synthetic_{n_samples}_{n_features}_{n_classes}_large_test"
    
    train_df = pd.DataFrame(X_train_scaled, columns=feature_names)
    train_df['target'] = y_train
    train_df.to_csv(f"../data/{dataset_name}_train.csv", index=False)
    print(f" Saved {dataset_name} training data: ../data/{dataset_name}_train.csv ({len(X_train)} samples)")
    
    test_df = pd.DataFrame(X_test_scaled, columns=feature_names)
    test_df['target'] = y_test
    test_df.to_csv(f"../data/{dataset_name}_test.csv", index=False)
    print(f" Saved {dataset_name} test data: ../data/{dataset_name}_test.csv ({len(X_test)} samples)")
    
    # Save full dataset
    full_df = pd.DataFrame(scaler.fit_transform(X), columns=feature_names)
    full_df['target'] = y
    full_df.to_csv(f"../data/{dataset_name}_full.csv", index=False)
    print(f" Saved {dataset_name} full data: ../data/{dataset_name}_full.csv ({len(X)} samples)")
    
    return len(X_train), len(X_test), X.shape[1], len(np.unique(y)), dataset_name

def print_dataset_summary(results):
    """Print summary of generated datasets."""
    print("\n" + "="*70)
    print("LARGE TEST DATASET SUMMARY")
    print("="*70)
    
    total_test_samples = 0
    for dataset_info in results:
        if len(dataset_info) == 4:
            train_samples, test_samples, features, classes = dataset_info
            dataset_name = "Unknown"
        else:
            train_samples, test_samples, features, classes, dataset_name = dataset_info
        
        total_test_samples += test_samples
        print(f"{dataset_name.replace('_', ' ').title():<30}: {test_samples:4d} test samples ({train_samples:3d} train)")
    
    print(f"\nTotal test samples across all datasets: {total_test_samples}")
    print(f"Average test samples per dataset: {total_test_samples/len(results):.0f}")
    
    print(f"\nNext steps:")
    print(f"   1. Update implementations to use *_large_test datasets")
    print(f"   2. Run controlled experiments with larger test sets")
    print(f"   3. Analyze statistical significance with more samples")

def main():
    parser = argparse.ArgumentParser(description="Generate datasets with larger test sets")
    parser.add_argument("--datasets", nargs='+', 
                       choices=['iris', 'wine', 'breast_cancer', 'synthetic', 'all'], 
                       default=['all'], help="Datasets to generate")
    parser.add_argument("--test-size", type=float, default=0.6, 
                       help="Test set proportion (default: 0.6)")
    parser.add_argument("--synthetic-samples", type=int, default=5000,
                       help="Number of synthetic samples (default: 5000)")
    parser.add_argument("--synthetic-test-size", type=float, default=0.7,
                       help="Test set proportion for synthetic data (default: 0.7)")
    
    args = parser.parse_args()
    
    print("Large Test Dataset Generation for KNN Experiment")
    print("="*60)
    
    create_data_directory()
    
    results = []
    datasets_to_generate = args.datasets if 'all' not in args.datasets else ['iris', 'wine', 'breast_cancer', 'synthetic']
    
    for dataset in datasets_to_generate:
        if dataset == 'iris':
            result = generate_iris_large_test(args.test_size)
            results.append(result + ("iris_large_test",))
        elif dataset == 'wine':
            result = generate_wine_large_test(args.test_size)
            results.append(result + ("wine_large_test",))
        elif dataset == 'breast_cancer':
            result = generate_breast_cancer_large_test(args.test_size)
            results.append(result + ("breast_cancer_large_test",))
        elif dataset == 'synthetic':
            result = generate_synthetic_large_test(
                n_samples=args.synthetic_samples,
                test_size=args.synthetic_test_size
            )
            results.append(result)
    
    print_dataset_summary(results)
    print(f"\nLarge test dataset generation completed!")

if __name__ == "__main__":
    main()
