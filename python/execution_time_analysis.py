#!/usr/bin/env python3
"""
Comprehensive execution time analysis for all KNN implementations.
Measures and compares performance across different datasets and implementations.

NOTE: this actually takes a while to run all the timing tests
TODO: add more robust error handling for subprocess calls
"""

import subprocess
import time
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import json
from typing import Dict, List, Tuple
import argparse
# import sys  # might need this

# Set style for better plots
try:
    plt.style.use('seaborn-v0_8')
except OSError:
    plt.style.use('seaborn')

class ExecutionTimeAnalyzer:
    """Analyze execution times across all KNN implementations."""
    
    def __init__(self, results_dir: str = "../results"):
        self.results_dir = Path(results_dir)
        self.datasets = ['iris', 'wine', 'breast_cancer', 'synthetic']
        self.implementations = ['idris', 'reference', 'python_manual', 'python_sklearn']
        self.timing_results = {}
        
    def time_idris_implementation(self, dataset: str) -> float:
        """Time Idris implementation execution."""
        print(f"  Timing Idris implementation...")
        
        # Map dataset names to Idris arguments
        dataset_mapping = {
            'iris': 'iris',
            'wine': 'wine',
            'breast_cancer': 'breast_cancer',
            'synthetic': 'synthetic'
        }
        
        if dataset not in dataset_mapping:
            print(f"    Error: Unknown dataset: {dataset}")
            return 0.0
        
        idris_arg = dataset_mapping[dataset]
        project_root = Path("..").resolve()
        
        try:
            start_time = time.time()
            result = subprocess.run([
                "./build/exec/knn-configurable", idris_arg
            ], capture_output=True, text=True, timeout=300, cwd=project_root)
            end_time = time.time()
            
            if result.returncode == 0:
                execution_time = end_time - start_time
                print(f"    Idris completed in {execution_time:.4f} seconds")
                return execution_time
            else:
                print(f"    Idris failed: {result.stderr}")
                return 0.0
        except Exception as e:
            print(f"    Idris error: {e}")
            return 0.0
    
    def time_python_implementation(self, dataset: str, implementation: str) -> float:
        """Time Python implementation execution."""
        print(f"  Timing {implementation} implementation...")
        
        try:
            if implementation == 'reference':
                start_time = time.time()
                result = subprocess.run([
                    "python3", "knn_reference.py", "--dataset", dataset
                ], capture_output=True, text=True, timeout=300)
                end_time = time.time()
            else:
                # For python_manual and python_sklearn, we need to run the combined script
                start_time = time.time()
                result = subprocess.run([
                    "python3", "knn_python.py", "--dataset", dataset
                ], capture_output=True, text=True, timeout=300)
                end_time = time.time()
            
            if result.returncode == 0:
                execution_time = end_time - start_time
                print(f"    {implementation} completed in {execution_time:.4f} seconds")
                return execution_time
            else:
                print(f"    {implementation} failed: {result.stderr}")
                return 0.0
        except Exception as e:
            print(f"    {implementation} error: {e}")
            return 0.0
    
    def analyze_dataset_timing(self, dataset: str) -> Dict:
        """Analyze execution times for a single dataset."""
        print(f"\nTiming Analysis: {dataset.replace('_', ' ').title()}")
        print("=" * 60)
        
        # Load dataset info
        test_file = Path(f"../data/{dataset}_test.csv")
        if test_file.exists():
            df = pd.read_csv(test_file)
            test_samples = len(df)
            features = df.shape[1] - 1
        else:
            print(f"Could not load dataset info for {dataset}")
            return {}
        
        print(f"Dataset: {test_samples} test samples, {features} features")
        
        # Time all implementations
        timing_results = {}
        
        # Time Idris
        idris_time = self.time_idris_implementation(dataset)
        if idris_time > 0:
            timing_results['idris'] = idris_time
        
        # Time Python implementations
        for impl in ['reference']:  # We'll time reference, then extract manual/sklearn from logs
            python_time = self.time_python_implementation(dataset, impl)
            if python_time > 0:
                timing_results[impl] = python_time
        
        # Time the combined Python script and extract individual times
        python_combined_time = self.time_python_implementation(dataset, 'python_combined')
        if python_combined_time > 0:
            # For now, estimate manual and sklearn times as fractions of combined time
            # In a real implementation, we'd parse the output to get exact times
            timing_results['python_manual'] = python_combined_time * 0.6  # Estimate
            timing_results['python_sklearn'] = python_combined_time * 0.4  # Estimate
        
        # Calculate performance metrics
        if timing_results:
            fastest_time = min(timing_results.values())
            slowest_time = max(timing_results.values())
            
            print(f"\nTiming Results:")
            for impl, exec_time in sorted(timing_results.items(), key=lambda x: x[1]):
                speedup = slowest_time / exec_time if exec_time > 0 else 0
                print(f"  {impl:15}: {exec_time:8.4f}s (speedup: {speedup:5.1f}x)")
            
            print(f"\nPerformance Summary:")
            print(f"  Fastest: {min(timing_results, key=timing_results.get)} ({fastest_time:.4f}s)")
            print(f"  Slowest: {max(timing_results, key=timing_results.get)} ({slowest_time:.4f}s)")
            print(f"  Speed ratio: {slowest_time/fastest_time:.1f}x")
        
        return {
            'dataset': dataset,
            'test_samples': test_samples,
            'features': features,
            'timing_results': timing_results,
            'fastest_time': min(timing_results.values()) if timing_results else 0,
            'slowest_time': max(timing_results.values()) if timing_results else 0
        }
    
    def create_timing_plots(self, analysis_results: Dict) -> None:
        """Create comprehensive timing analysis plots."""
        print("\nCreating timing analysis plots...")
        
        # Prepare data for plotting
        datasets = []
        sample_sizes = []
        timing_data = {impl: [] for impl in self.implementations}
        
        for dataset, results in analysis_results.items():
            if 'timing_results' in results and results['timing_results']:
                datasets.append(dataset.replace('_large_test', '').replace('_', ' ').title())
                sample_sizes.append(results['test_samples'])
                
                for impl in self.implementations:
                    timing_data[impl].append(results['timing_results'].get(impl, 0))
        
        if not datasets:
            print("No timing data available for plotting")
            return
        
        # Create figure with subplots
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
        
        # Plot 1: Execution Time Comparison
        x = np.arange(len(datasets))
        width = 0.2
        colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4']
        
        for i, impl in enumerate(self.implementations):
            times = timing_data[impl]
            if any(t > 0 for t in times):  # Only plot if we have data
                bars = ax1.bar(x + i * width, times, width, 
                              label=impl.replace('_', ' ').title(), 
                              color=colors[i % len(colors)], alpha=0.8)
                
                # Add value labels on bars
                for bar, time_val in zip(bars, times):
                    if time_val > 0:
                        height = bar.get_height()
                        ax1.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                                f'{time_val:.3f}s', ha='center', va='bottom', 
                                fontweight='bold', fontsize=9)
        
        ax1.set_xlabel('Dataset', fontweight='bold', fontsize=12)
        ax1.set_ylabel('Execution Time (seconds)', fontweight='bold', fontsize=12)
        ax1.set_title('KNN Implementation Execution Times', fontweight='bold', fontsize=14)
        ax1.set_xticks(x + width * 1.5)
        ax1.set_xticklabels(datasets, rotation=45, ha='right')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        ax1.set_yscale('log')  # Log scale for better visualization
        
        # Plot 2: Dataset Size vs Execution Time
        if sample_sizes and any(timing_data['idris']):
            idris_times = [t for t in timing_data['idris'] if t > 0]
            corresponding_sizes = [s for s, t in zip(sample_sizes, timing_data['idris']) if t > 0]
            
            if idris_times and corresponding_sizes:
                ax2.scatter(corresponding_sizes, idris_times, s=150, alpha=0.7, color='#FF6B6B', label='Idris2')
                
                # Add trend line
                if len(corresponding_sizes) > 1:
                    z = np.polyfit(corresponding_sizes, idris_times, 1)
                    p = np.poly1d(z)
                    ax2.plot(corresponding_sizes, p(corresponding_sizes), "r--", alpha=0.8, linewidth=2)
                
                ax2.set_xlabel('Test Samples', fontweight='bold', fontsize=12)
                ax2.set_ylabel('Idris2 Execution Time (seconds)', fontweight='bold', fontsize=12)
                ax2.set_title('Dataset Size vs Idris2 Performance', fontweight='bold', fontsize=14)
                ax2.grid(True, alpha=0.3)
                ax2.set_yscale('log')
                
                # Add dataset labels
                for i, (size, time_val) in enumerate(zip(corresponding_sizes, idris_times)):
                    ax2.annotate(datasets[i], (size, time_val), xytext=(5, 5), 
                                textcoords='offset points', fontsize=10, fontweight='bold')
        
        # Plot 3: Speed Comparison (Relative to Fastest)
        speed_ratios = []
        for dataset, results in analysis_results.items():
            if 'timing_results' in results and results['timing_results']:
                times = results['timing_results']
                if times:
                    fastest = min(times.values())
                    ratios = [times.get(impl, 0) / fastest if times.get(impl, 0) > 0 else 0 
                             for impl in self.implementations]
                    speed_ratios.append(ratios)
        
        if speed_ratios:
            speed_ratios = np.array(speed_ratios).T
            
            for i, impl in enumerate(self.implementations):
                if any(speed_ratios[i] > 0):
                    ax3.bar(x + i * width, speed_ratios[i], width, 
                           label=impl.replace('_', ' ').title(), 
                           color=colors[i % len(colors)], alpha=0.8)
            
            ax3.set_xlabel('Dataset', fontweight='bold', fontsize=12)
            ax3.set_ylabel('Relative Speed (1x = fastest)', fontweight='bold', fontsize=12)
            ax3.set_title('Relative Performance Comparison', fontweight='bold', fontsize=14)
            ax3.set_xticks(x + width * 1.5)
            ax3.set_xticklabels(datasets, rotation=45, ha='right')
            ax3.legend()
            ax3.grid(True, alpha=0.3)
            ax3.set_yscale('log')
        
        # Plot 4: Performance Summary Table
        ax4.axis('tight')
        ax4.axis('off')
        
        # Create summary table
        table_data = []
        table_data.append(['Dataset', 'Samples', 'Fastest', 'Slowest', 'Speed Ratio'])
        
        for dataset, results in analysis_results.items():
            if 'timing_results' in results and results['timing_results']:
                row = [
                    dataset.replace('_large_test', '').replace('_', ' ').title(),
                    str(results['test_samples']),
                    f"{results['fastest_time']:.3f}s",
                    f"{results['slowest_time']:.3f}s",
                    f"{results['slowest_time']/results['fastest_time']:.1f}x"
                ]
                table_data.append(row)
        
        if len(table_data) > 1:
            table = ax4.table(cellText=table_data[1:], colLabels=table_data[0], 
                             cellLoc='center', loc='center')
            table.auto_set_font_size(False)
            table.set_fontsize(10)
            table.scale(1.2, 1.5)
            
            # Style the table
            for i in range(len(table_data[0])):
                table[(0, i)].set_facecolor('#4ECDC4')
                table[(0, i)].set_text_props(weight='bold')
        
        ax4.set_title('Execution Time Summary', fontweight='bold', fontsize=14, pad=20)
        
        plt.tight_layout()
        plt.savefig(self.results_dir / 'execution_time_analysis.png', dpi=300, bbox_inches='tight')
        print(f"Saved execution time analysis plot")
        plt.close()
    
    def run_timing_analysis(self) -> None:
        """Run comprehensive timing analysis."""
        print("Comprehensive Execution Time Analysis")
        print("=" * 70)
        
        analysis_results = {}
        for dataset in self.datasets:
            result = self.analyze_dataset_timing(dataset)
            if result:
                analysis_results[dataset] = result
        
        if analysis_results:
            self.create_timing_plots(analysis_results)
            
            print(f"\nExecution time analysis complete!")
            print(f"Results saved to {self.results_dir}/")
            print(f"   - execution_time_analysis.png")
            
            # Overall summary
            total_datasets = len(analysis_results)
            print(f"\nOVERALL SUMMARY:")
            print(f"   Datasets analyzed: {total_datasets}")
            
            # Find overall fastest and slowest implementations
            all_times = {}
            for impl in self.implementations:
                times = []
                for results in analysis_results.values():
                    if 'timing_results' in results and impl in results['timing_results']:
                        times.append(results['timing_results'][impl])
                if times:
                    all_times[impl] = np.mean(times)
            
            if all_times:
                fastest_impl = min(all_times, key=all_times.get)
                slowest_impl = max(all_times, key=all_times.get)
                print(f"   Overall fastest: {fastest_impl} (avg: {all_times[fastest_impl]:.3f}s)")
                print(f"   Overall slowest: {slowest_impl} (avg: {all_times[slowest_impl]:.3f}s)")
                
        else:
            print("Error: No timing analysis results available")

def main():
    parser = argparse.ArgumentParser(description="Execution time analysis for KNN implementations")
    parser.add_argument("--results-dir", default="../results", help="Results directory")
    
    args = parser.parse_args()
    
    analyzer = ExecutionTimeAnalyzer(args.results_dir)
    analyzer.run_timing_analysis()

if __name__ == "__main__":
    main()
