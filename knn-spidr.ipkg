package knn-spidr

version    = 0.1.0
authors    = "<PERSON><PERSON>ab<PERSON> Patil"
brief      = "Spidr-based K-Nearest Neighbors classifier with dependent types and hardware acceleration"
readme     = "README.md"
license    = "MIT"

depends = base >= 0.5.0
        , contrib >= 0.5.0
        , spidr
        , pjrt-plugin-xla-cpu
        

sourcedir = "src"
modules   = KNN_Spidr

executable = "knn-spidr"
main       = KNN_Spidr
