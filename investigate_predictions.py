#!/usr/bin/env python3
"""
Investigation of Prediction Differences
=======================================

This script investigates why certain models achieve 100% accuracy on Iris
and why some models have identical average accuracies.
"""

import pandas as pd
import numpy as np

def load_ground_truth(dataset):
    """Load ground truth labels"""
    df = pd.read_csv(f"data/{dataset}_test.csv")
    return df.iloc[:, -1].astype(int).tolist()

def load_predictions(model, dataset):
    """Load predictions from file"""
    try:
        with open(f"results/{model}_{dataset}_predictions.txt", 'r') as f:
            predictions = [int(line.strip()) for line in f if line.strip()]
        return predictions
    except FileNotFoundError:
        return None

def compare_predictions():
    """Compare predictions across all models and datasets"""
    
    models = ["python_sklearn", "python_manual", "idris", "spidr", "reference"]
    datasets = ["iris", "wine", "breast_cancer", "synthetic"]
    
    print("DETAILED PREDICTION ANALYSIS")
    print("=" * 50)
    
    for dataset in datasets:
        print(f"\n📊 {dataset.upper()} DATASET ANALYSIS")
        print("-" * 40)
        
        # Load ground truth
        ground_truth = load_ground_truth(dataset)
        print(f"Ground truth length: {len(ground_truth)}")
        
        # Load all predictions
        all_predictions = {}
        for model in models:
            preds = load_predictions(model, dataset)
            if preds:
                all_predictions[model] = preds
                print(f"{model} predictions length: {len(preds)}")
        
        if not all_predictions:
            print("No predictions found for this dataset")
            continue
            
        # Calculate accuracies
        print(f"\n🎯 ACCURACY BREAKDOWN:")
        accuracies = {}
        for model, preds in all_predictions.items():
            # Ensure same length for comparison
            min_len = min(len(preds), len(ground_truth))
            preds_trimmed = preds[:min_len]
            truth_trimmed = ground_truth[:min_len]
            
            correct = sum(1 for p, t in zip(preds_trimmed, truth_trimmed) if p == t)
            accuracy = correct / min_len * 100
            accuracies[model] = accuracy
            
            print(f"  {model:20}: {accuracy:6.2f}% ({correct}/{min_len})")
        
        # Find differences
        print(f"\n🔍 PREDICTION DIFFERENCES:")
        if len(all_predictions) > 1:
            # Compare each model with ground truth
            for model, preds in all_predictions.items():
                min_len = min(len(preds), len(ground_truth))
                differences = []
                for i in range(min_len):
                    if preds[i] != ground_truth[i]:
                        differences.append((i, preds[i], ground_truth[i]))
                
                if differences:
                    print(f"  {model} errors:")
                    for idx, pred, truth in differences:
                        print(f"    Sample {idx+1}: predicted {pred}, actual {truth}")
                else:
                    print(f"  {model}: PERFECT (100% accuracy)")
        
        # Compare models with each other
        print(f"\n🔄 INTER-MODEL COMPARISON:")
        model_list = list(all_predictions.keys())
        for i in range(len(model_list)):
            for j in range(i+1, len(model_list)):
                model1, model2 = model_list[i], model_list[j]
                preds1, preds2 = all_predictions[model1], all_predictions[model2]
                
                min_len = min(len(preds1), len(preds2))
                agreements = sum(1 for p1, p2 in zip(preds1[:min_len], preds2[:min_len]) if p1 == p2)
                agreement_rate = agreements / min_len * 100
                
                print(f"  {model1} ↔ {model2}: {agreement_rate:.1f}% agreement ({agreements}/{min_len})")
                
                # Show disagreements
                disagreements = []
                for k in range(min_len):
                    if preds1[k] != preds2[k]:
                        disagreements.append((k, preds1[k], preds2[k]))
                
                if disagreements and len(disagreements) <= 5:  # Show only if few disagreements
                    print(f"    Disagreements:")
                    for idx, p1, p2 in disagreements:
                        print(f"      Sample {idx+1}: {model1}={p1}, {model2}={p2}")

def analyze_iris_perfection():
    """Analyze why Iris gets near-perfect results"""
    print(f"\n🌸 IRIS DATASET DEEP DIVE")
    print("=" * 30)
    
    # Load the actual iris test data
    df = pd.read_csv("data/iris_test.csv")
    print(f"Iris test set shape: {df.shape}")
    
    # Check class distribution
    class_counts = df.iloc[:, -1].value_counts().sort_index()
    print(f"Class distribution: {dict(class_counts)}")
    
    # Check if data is well-separated
    features = df.iloc[:, :-1].values
    labels = df.iloc[:, -1].values
    
    print(f"\nFeature statistics:")
    for i, col in enumerate(df.columns[:-1]):
        print(f"  {col}: mean={features[:, i].mean():.3f}, std={features[:, i].std():.3f}")
    
    # Calculate inter-class distances
    print(f"\nClass separation analysis:")
    for class_id in sorted(df.iloc[:, -1].unique()):
        class_data = features[labels == class_id]
        other_data = features[labels != class_id]
        
        if len(class_data) > 0 and len(other_data) > 0:
            # Calculate minimum distance from this class to other classes
            min_distances = []
            for point in class_data:
                distances = [np.linalg.norm(point - other_point) for other_point in other_data]
                min_distances.append(min(distances))
            
            avg_min_distance = np.mean(min_distances)
            print(f"  Class {class_id}: avg min distance to other classes = {avg_min_distance:.3f}")

def analyze_identical_accuracies():
    """Analyze why some models have identical average accuracies"""
    print(f"\n🎯 IDENTICAL ACCURACY ANALYSIS")
    print("=" * 35)
    
    models = ["python_sklearn", "python_manual", "spidr"]
    datasets = ["iris", "wine", "breast_cancer", "synthetic"]
    
    # Calculate detailed accuracies
    model_accuracies = {model: [] for model in models}
    
    for dataset in datasets:
        ground_truth = load_ground_truth(dataset)
        
        for model in models:
            preds = load_predictions(model, dataset)
            if preds:
                min_len = min(len(preds), len(ground_truth))
                correct = sum(1 for p, t in zip(preds[:min_len], ground_truth[:min_len]) if p == t)
                accuracy = correct / min_len * 100
                model_accuracies[model].append((dataset, accuracy, correct, min_len))
    
    # Show detailed breakdown
    for model in models:
        print(f"\n{model.upper()} DETAILED BREAKDOWN:")
        total_correct = 0
        total_samples = 0
        
        for dataset, accuracy, correct, total in model_accuracies[model]:
            print(f"  {dataset:15}: {accuracy:6.2f}% ({correct:3d}/{total:3d})")
            total_correct += correct
            total_samples += total
        
        overall_accuracy = total_correct / total_samples * 100 if total_samples > 0 else 0
        print(f"  {'OVERALL':15}: {overall_accuracy:6.2f}% ({total_correct:3d}/{total_samples:3d})")
    
    # Check if they're using identical algorithms
    print(f"\n🔍 ALGORITHM SIMILARITY CHECK:")
    
    # Compare predictions pairwise
    for dataset in datasets:
        sklearn_preds = load_predictions("python_sklearn", dataset)
        manual_preds = load_predictions("python_manual", dataset)
        spidr_preds = load_predictions("spidr", dataset)
        
        if sklearn_preds and manual_preds and spidr_preds:
            min_len = min(len(sklearn_preds), len(manual_preds), len(spidr_preds))
            
            sklearn_manual_agreement = sum(1 for i in range(min_len) if sklearn_preds[i] == manual_preds[i])
            sklearn_spidr_agreement = sum(1 for i in range(min_len) if sklearn_preds[i] == spidr_preds[i])
            manual_spidr_agreement = sum(1 for i in range(min_len) if manual_preds[i] == spidr_preds[i])
            
            print(f"  {dataset}:")
            print(f"    sklearn ↔ manual: {sklearn_manual_agreement/min_len*100:.1f}%")
            print(f"    sklearn ↔ spidr:  {sklearn_spidr_agreement/min_len*100:.1f}%")
            print(f"    manual ↔ spidr:   {manual_spidr_agreement/min_len*100:.1f}%")

if __name__ == "__main__":
    compare_predictions()
    analyze_iris_perfection()
    analyze_identical_accuracies()
    
    print(f"\n" + "="*60)
    print("SUMMARY OF FINDINGS:")
    print("="*60)
    print("1. Iris dataset analysis will show class separability")
    print("2. Model comparison will reveal implementation similarities")
    print("3. Accuracy breakdown will explain identical averages")
    print("="*60)
