#!/usr/bin/env python3
"""
Comprehensive Analysis of KNN Implementations
==============================================

This script analyzes all KNN implementations across all datasets and generates
comprehensive plots and analysis documents.

Models analyzed:
- Python scikit-learn KNN
- Python manual KNN
- Idris2 original KNN
- Idris2 Spidr-based KNN
- Reference implementation

Datasets:
- Iris (4 features, 3 classes)
- Wine (13 features, 3 classes)
- Breast Cancer (30 features, 2 classes)
- Synthetic (12 features, 5 classes)
"""

import os
import json
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import re
from collections import defaultdict
import time

# Set up plotting style
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

class ComprehensiveAnalyzer:
    def __init__(self, results_dir="results"):
        self.results_dir = Path(results_dir)
        self.datasets = ["iris", "wine", "breast_cancer", "synthetic"]
        self.models = ["python_sklearn", "python_manual", "idris", "spidr", "reference"]
        self.data = defaultdict(lambda: defaultdict(dict))
        self.predictions = defaultdict(lambda: defaultdict(dict))
        
    def load_predictions(self, model, dataset):
        """Load predictions for a specific model and dataset"""
        prediction_files = {
            "python_sklearn": f"python_sklearn_{dataset}_predictions.txt",
            "python_manual": f"python_manual_{dataset}_predictions.txt", 
            "idris": f"idris_{dataset}_predictions.txt",
            "spidr": f"spidr_{dataset}_predictions.txt",
            "reference": f"reference_{dataset}_predictions.txt"
        }
        
        file_path = self.results_dir / prediction_files.get(model, f"{model}_{dataset}_predictions.txt")
        
        if file_path.exists():
            with open(file_path, 'r') as f:
                predictions = [int(line.strip()) for line in f if line.strip()]
            return predictions
        return None
    
    def load_ground_truth(self, dataset):
        """Load ground truth labels from test data"""
        test_files = {
            "iris": "data/iris_test.csv",
            "wine": "data/wine_test.csv", 
            "breast_cancer": "data/breast_cancer_test.csv",
            "synthetic": "data/synthetic_test.csv"
        }
        
        file_path = Path(test_files[dataset])
        if file_path.exists():
            df = pd.read_csv(file_path)
            return df.iloc[:, -1].astype(int).tolist()  # Last column is target
        return None
    
    def calculate_accuracy(self, predictions, ground_truth):
        """Calculate accuracy between predictions and ground truth"""
        if predictions is None or ground_truth is None:
            return None
        
        if len(predictions) != len(ground_truth):
            print(f"Warning: Length mismatch - predictions: {len(predictions)}, ground_truth: {len(ground_truth)}")
            min_len = min(len(predictions), len(ground_truth))
            predictions = predictions[:min_len]
            ground_truth = ground_truth[:min_len]
        
        correct = sum(1 for p, g in zip(predictions, ground_truth) if p == g)
        return correct / len(ground_truth) * 100
    
    def load_performance_metrics(self):
        """Load performance metrics from various sources"""
        
        # Load Spidr metrics
        for dataset in self.datasets:
            metrics_file = self.results_dir / f"spidr_{dataset}_metrics.txt"
            if metrics_file.exists():
                with open(metrics_file, 'r') as f:
                    content = f.read()
                    
                # Parse accuracy
                acc_match = re.search(r'Accuracy: ([\d.]+)%', content)
                if acc_match:
                    self.data["spidr"][dataset]["accuracy"] = float(acc_match.group(1))
                
                # Parse execution time
                time_match = re.search(r'Execution time: ([\d.]+) ms', content)
                if time_match:
                    self.data["spidr"][dataset]["execution_time_ms"] = float(time_match.group(1))
                
                # Parse memory usage
                mem_match = re.search(r'Memory usage: ([\d.]+) MB', content)
                if mem_match:
                    self.data["spidr"][dataset]["memory_usage_mb"] = float(mem_match.group(1))
        
        # Load Python experiment results
        for dataset in self.datasets:
            for model_type in ["sklearn", "manual"]:
                results_file = self.results_dir / f"python_{dataset}_experiment_results.txt"
                if results_file.exists():
                    with open(results_file, 'r') as f:
                        content = f.read()
                        
                    # Parse results for both sklearn and manual
                    if model_type == "sklearn":
                        acc_pattern = r'Scikit-learn KNN Accuracy: ([\d.]+)%'
                        time_pattern = r'Scikit-learn KNN Execution Time: ([\d.]+) seconds'
                        mem_pattern = r'Scikit-learn KNN Memory Usage: ([\d.]+) MB'
                    else:
                        acc_pattern = r'Manual KNN Accuracy: ([\d.]+)%'
                        time_pattern = r'Manual KNN Execution Time: ([\d.]+) seconds'
                        mem_pattern = r'Manual KNN Memory Usage: ([\d.]+) MB'
                    
                    acc_match = re.search(acc_pattern, content)
                    if acc_match:
                        self.data[f"python_{model_type}"][dataset]["accuracy"] = float(acc_match.group(1))
                    
                    time_match = re.search(time_pattern, content)
                    if time_match:
                        self.data[f"python_{model_type}"][dataset]["execution_time_ms"] = float(time_match.group(1)) * 1000
                    
                    mem_match = re.search(mem_pattern, content)
                    if mem_match:
                        self.data[f"python_{model_type}"][dataset]["memory_usage_mb"] = float(mem_match.group(1))
        
        # Load reference implementation results
        for dataset in self.datasets:
            summary_file = self.results_dir / f"reference_{dataset}_summary.json"
            if summary_file.exists():
                with open(summary_file, 'r') as f:
                    data = json.load(f)
                    self.data["reference"][dataset]["accuracy"] = data.get("accuracy", 0) * 100
                    self.data["reference"][dataset]["execution_time_ms"] = data.get("execution_time_seconds", 0) * 1000
                    self.data["reference"][dataset]["memory_usage_mb"] = data.get("memory_usage_mb", 0)
    
    def calculate_all_accuracies(self):
        """Calculate accuracies for all models and datasets"""
        for dataset in self.datasets:
            ground_truth = self.load_ground_truth(dataset)
            if ground_truth is None:
                continue
                
            for model in self.models:
                predictions = self.load_predictions(model, dataset)
                if predictions is not None:
                    accuracy = self.calculate_accuracy(predictions, ground_truth)
                    if accuracy is not None:
                        self.data[model][dataset]["accuracy"] = accuracy
                        self.predictions[model][dataset] = predictions
    
    def measure_execution_times(self):
        """Measure execution times for Idris implementations"""
        print("Measuring execution times for Idris implementations...")
        
        # Measure original Idris KNN
        for dataset in self.datasets:
            if dataset == "iris":  # Only iris is implemented in original
                print(f"Measuring Idris execution time for {dataset}...")
                start_time = time.time()
                os.system("./build/exec/knn-classifier > /dev/null 2>&1")
                end_time = time.time()
                execution_time_ms = (end_time - start_time) * 1000
                self.data["idris"][dataset]["execution_time_ms"] = execution_time_ms
        
        # Measure Spidr KNN execution times
        for dataset in self.datasets:
            print(f"Measuring Spidr execution time for {dataset}...")
            start_time = time.time()
            os.system(f"./build/exec/knn-spidr {dataset} > /dev/null 2>&1")
            end_time = time.time()
            execution_time_ms = (end_time - start_time) * 1000
            # Update with actual measured time
            if "spidr" in self.data and dataset in self.data["spidr"]:
                self.data["spidr"][dataset]["execution_time_ms"] = execution_time_ms
    
    def generate_accuracy_comparison_plot(self):
        """Generate accuracy comparison plot across all models and datasets"""
        fig, ax = plt.subplots(figsize=(12, 8))
        
        datasets_with_data = []
        model_accuracies = {model: [] for model in self.models}
        
        for dataset in self.datasets:
            has_data = False
            for model in self.models:
                if model in self.data and dataset in self.data[model] and "accuracy" in self.data[model][dataset]:
                    has_data = True
                    break
            
            if has_data:
                datasets_with_data.append(dataset.replace('_', ' ').title())
                for model in self.models:
                    if model in self.data and dataset in self.data[model] and "accuracy" in self.data[model][dataset]:
                        model_accuracies[model].append(self.data[model][dataset]["accuracy"])
                    else:
                        model_accuracies[model].append(0)
        
        x = np.arange(len(datasets_with_data))
        width = 0.15
        
        model_labels = {
            "python_sklearn": "Python scikit-learn",
            "python_manual": "Python Manual", 
            "idris": "Idris2 Original",
            "spidr": "Idris2 Spidr",
            "reference": "Reference"
        }
        
        colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd']
        
        for i, model in enumerate(self.models):
            if any(acc > 0 for acc in model_accuracies[model]):
                ax.bar(x + i * width, model_accuracies[model], width, 
                      label=model_labels[model], color=colors[i], alpha=0.8)
        
        ax.set_xlabel('Dataset', fontsize=12)
        ax.set_ylabel('Accuracy (%)', fontsize=12)
        ax.set_title('KNN Classification Accuracy Comparison Across All Implementations', fontsize=14, fontweight='bold')
        ax.set_xticks(x + width * 2)
        ax.set_xticklabels(datasets_with_data)
        ax.legend()
        ax.grid(True, alpha=0.3)
        ax.set_ylim(0, 105)
        
        # Add value labels on bars
        for i, model in enumerate(self.models):
            for j, acc in enumerate(model_accuracies[model]):
                if acc > 0:
                    ax.text(j + i * width, acc + 1, f'{acc:.1f}%', 
                           ha='center', va='bottom', fontsize=9)
        
        plt.tight_layout()
        plt.savefig(self.results_dir / 'comprehensive_accuracy_comparison.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        return fig

    def generate_execution_time_plot(self):
        """Generate execution time comparison plot"""
        fig, ax = plt.subplots(figsize=(12, 8))

        datasets_with_data = []
        model_times = {model: [] for model in self.models}

        for dataset in self.datasets:
            has_data = False
            for model in self.models:
                if (model in self.data and dataset in self.data[model] and
                    "execution_time_ms" in self.data[model][dataset] and
                    self.data[model][dataset]["execution_time_ms"] > 0):
                    has_data = True
                    break

            if has_data:
                datasets_with_data.append(dataset.replace('_', ' ').title())
                for model in self.models:
                    if (model in self.data and dataset in self.data[model] and
                        "execution_time_ms" in self.data[model][dataset]):
                        model_times[model].append(self.data[model][dataset]["execution_time_ms"])
                    else:
                        model_times[model].append(0)

        x = np.arange(len(datasets_with_data))
        width = 0.15

        model_labels = {
            "python_sklearn": "Python scikit-learn",
            "python_manual": "Python Manual",
            "idris": "Idris2 Original",
            "spidr": "Idris2 Spidr",
            "reference": "Reference"
        }

        colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd']

        for i, model in enumerate(self.models):
            if any(time > 0 for time in model_times[model]):
                ax.bar(x + i * width, model_times[model], width,
                      label=model_labels[model], color=colors[i], alpha=0.8)

        ax.set_xlabel('Dataset', fontsize=12)
        ax.set_ylabel('Execution Time (ms)', fontsize=12)
        ax.set_title('KNN Execution Time Comparison Across All Implementations', fontsize=14, fontweight='bold')
        ax.set_xticks(x + width * 2)
        ax.set_xticklabels(datasets_with_data)
        ax.legend()
        ax.grid(True, alpha=0.3)
        ax.set_yscale('log')  # Use log scale for better visualization

        # Add value labels on bars
        for i, model in enumerate(self.models):
            for j, time_val in enumerate(model_times[model]):
                if time_val > 0:
                    ax.text(j + i * width, time_val * 1.1, f'{time_val:.1f}ms',
                           ha='center', va='bottom', fontsize=9, rotation=45)

        plt.tight_layout()
        plt.savefig(self.results_dir / 'comprehensive_execution_time_comparison.png', dpi=300, bbox_inches='tight')
        plt.close()

        return fig

    def generate_memory_usage_plot(self):
        """Generate memory usage comparison plot"""
        fig, ax = plt.subplots(figsize=(12, 8))

        datasets_with_data = []
        model_memory = {model: [] for model in self.models}

        for dataset in self.datasets:
            has_data = False
            for model in self.models:
                if (model in self.data and dataset in self.data[model] and
                    "memory_usage_mb" in self.data[model][dataset] and
                    self.data[model][dataset]["memory_usage_mb"] > 0):
                    has_data = True
                    break

            if has_data:
                datasets_with_data.append(dataset.replace('_', ' ').title())
                for model in self.models:
                    if (model in self.data and dataset in self.data[model] and
                        "memory_usage_mb" in self.data[model][dataset]):
                        model_memory[model].append(self.data[model][dataset]["memory_usage_mb"])
                    else:
                        model_memory[model].append(0)

        x = np.arange(len(datasets_with_data))
        width = 0.15

        model_labels = {
            "python_sklearn": "Python scikit-learn",
            "python_manual": "Python Manual",
            "idris": "Idris2 Original",
            "spidr": "Idris2 Spidr",
            "reference": "Reference"
        }

        colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd']

        for i, model in enumerate(self.models):
            if any(mem > 0 for mem in model_memory[model]):
                ax.bar(x + i * width, model_memory[model], width,
                      label=model_labels[model], color=colors[i], alpha=0.8)

        ax.set_xlabel('Dataset', fontsize=12)
        ax.set_ylabel('Memory Usage (MB)', fontsize=12)
        ax.set_title('KNN Memory Usage Comparison Across All Implementations', fontsize=14, fontweight='bold')
        ax.set_xticks(x + width * 2)
        ax.set_xticklabels(datasets_with_data)
        ax.legend()
        ax.grid(True, alpha=0.3)

        # Add value labels on bars
        for i, model in enumerate(self.models):
            for j, mem_val in enumerate(model_memory[model]):
                if mem_val > 0:
                    ax.text(j + i * width, mem_val + max(model_memory[model]) * 0.01, f'{mem_val:.1f}MB',
                           ha='center', va='bottom', fontsize=9)

        plt.tight_layout()
        plt.savefig(self.results_dir / 'comprehensive_memory_usage_comparison.png', dpi=300, bbox_inches='tight')
        plt.close()

        return fig

    def generate_prediction_agreement_matrix(self):
        """Generate prediction agreement matrix between models"""
        for dataset in self.datasets:
            models_with_predictions = []
            predictions_data = []

            for model in self.models:
                if model in self.predictions and dataset in self.predictions[model]:
                    models_with_predictions.append(model)
                    predictions_data.append(self.predictions[model][dataset])

            if len(models_with_predictions) < 2:
                continue

            # Calculate agreement matrix
            n_models = len(models_with_predictions)
            agreement_matrix = np.zeros((n_models, n_models))

            for i in range(n_models):
                for j in range(n_models):
                    if i == j:
                        agreement_matrix[i][j] = 100.0
                    else:
                        pred_i = predictions_data[i]
                        pred_j = predictions_data[j]
                        min_len = min(len(pred_i), len(pred_j))
                        agreements = sum(1 for k in range(min_len) if pred_i[k] == pred_j[k])
                        agreement_matrix[i][j] = (agreements / min_len) * 100

            # Plot agreement matrix
            fig, ax = plt.subplots(figsize=(10, 8))

            model_labels = {
                "python_sklearn": "Python\nscikit-learn",
                "python_manual": "Python\nManual",
                "idris": "Idris2\nOriginal",
                "spidr": "Idris2\nSpidr",
                "reference": "Reference"
            }

            labels = [model_labels.get(model, model) for model in models_with_predictions]

            im = ax.imshow(agreement_matrix, cmap='RdYlGn', vmin=0, vmax=100)

            # Add text annotations
            for i in range(n_models):
                for j in range(n_models):
                    text = ax.text(j, i, f'{agreement_matrix[i, j]:.1f}%',
                                 ha="center", va="center", color="black", fontweight='bold')

            ax.set_xticks(range(n_models))
            ax.set_yticks(range(n_models))
            ax.set_xticklabels(labels)
            ax.set_yticklabels(labels)
            ax.set_title(f'Prediction Agreement Matrix - {dataset.replace("_", " ").title()} Dataset',
                        fontsize=14, fontweight='bold')

            # Add colorbar
            cbar = plt.colorbar(im, ax=ax)
            cbar.set_label('Agreement Percentage (%)', rotation=270, labelpad=20)

            plt.tight_layout()
            plt.savefig(self.results_dir / f'prediction_agreement_{dataset}.png', dpi=300, bbox_inches='tight')
            plt.close()

    def generate_comprehensive_summary_plot(self):
        """Generate a comprehensive summary plot with multiple metrics"""
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))

        # Accuracy comparison
        datasets_with_data = []
        model_accuracies = {model: [] for model in self.models}

        for dataset in self.datasets:
            has_data = any(model in self.data and dataset in self.data[model] and "accuracy" in self.data[model][dataset]
                          for model in self.models)
            if has_data:
                datasets_with_data.append(dataset.replace('_', ' ').title())
                for model in self.models:
                    if model in self.data and dataset in self.data[model] and "accuracy" in self.data[model][dataset]:
                        model_accuracies[model].append(self.data[model][dataset]["accuracy"])
                    else:
                        model_accuracies[model].append(0)

        x = np.arange(len(datasets_with_data))
        width = 0.15
        colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd']
        model_labels = {
            "python_sklearn": "Python scikit-learn",
            "python_manual": "Python Manual",
            "idris": "Idris2 Original",
            "spidr": "Idris2 Spidr",
            "reference": "Reference"
        }

        for i, model in enumerate(self.models):
            if any(acc > 0 for acc in model_accuracies[model]):
                ax1.bar(x + i * width, model_accuracies[model], width,
                       label=model_labels[model], color=colors[i], alpha=0.8)

        ax1.set_xlabel('Dataset')
        ax1.set_ylabel('Accuracy (%)')
        ax1.set_title('Accuracy Comparison')
        ax1.set_xticks(x + width * 2)
        ax1.set_xticklabels(datasets_with_data)
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # Dataset characteristics
        dataset_info = {
            "Iris": {"samples": 150, "features": 4, "classes": 3},
            "Wine": {"samples": 178, "features": 13, "classes": 3},
            "Breast Cancer": {"samples": 569, "features": 30, "classes": 2},
            "Synthetic": {"samples": 5000, "features": 12, "classes": 5}
        }

        datasets = list(dataset_info.keys())
        features = [dataset_info[d]["features"] for d in datasets]
        samples = [dataset_info[d]["samples"] for d in datasets]

        ax2.scatter(features, samples, s=[dataset_info[d]["classes"]*100 for d in datasets],
                   c=range(len(datasets)), cmap='viridis', alpha=0.7)

        for i, dataset in enumerate(datasets):
            ax2.annotate(dataset, (features[i], samples[i]), xytext=(5, 5),
                        textcoords='offset points', fontsize=10)

        ax2.set_xlabel('Number of Features')
        ax2.set_ylabel('Number of Samples')
        ax2.set_title('Dataset Characteristics\n(Bubble size = Number of Classes)')
        ax2.grid(True, alpha=0.3)

        # Model performance summary (accuracy vs execution time)
        ax3.set_xlabel('Execution Time (ms)')
        ax3.set_ylabel('Average Accuracy (%)')
        ax3.set_title('Performance vs Speed Trade-off')

        for model in self.models:
            accuracies = []
            times = []
            for dataset in self.datasets:
                if (model in self.data and dataset in self.data[model] and
                    "accuracy" in self.data[model][dataset] and
                    "execution_time_ms" in self.data[model][dataset]):
                    accuracies.append(self.data[model][dataset]["accuracy"])
                    times.append(self.data[model][dataset]["execution_time_ms"])

            if accuracies and times:
                avg_acc = np.mean(accuracies)
                avg_time = np.mean(times)
                ax3.scatter(avg_time, avg_acc, s=100, label=model_labels[model], alpha=0.8)

        ax3.legend()
        ax3.grid(True, alpha=0.3)

        # Implementation comparison table
        ax4.axis('tight')
        ax4.axis('off')

        # Create summary table
        table_data = []
        headers = ['Model', 'Language', 'Type Safety', 'Hardware Accel.', 'Avg Accuracy']

        model_info = {
            "python_sklearn": ["Python scikit-learn", "Python", "Runtime", "CPU/GPU", ""],
            "python_manual": ["Python Manual", "Python", "Runtime", "CPU", ""],
            "idris": ["Idris2 Original", "Idris2", "Compile-time", "CPU", ""],
            "spidr": ["Idris2 Spidr", "Idris2", "Compile-time", "CPU/XLA", ""],
            "reference": ["Reference", "Python", "Runtime", "CPU", ""]
        }

        for model in self.models:
            if model in model_info:
                row = model_info[model].copy()
                # Calculate average accuracy
                accuracies = []
                for dataset in self.datasets:
                    if (model in self.data and dataset in self.data[model] and
                        "accuracy" in self.data[model][dataset]):
                        accuracies.append(self.data[model][dataset]["accuracy"])

                if accuracies:
                    row[4] = f"{np.mean(accuracies):.1f}%"
                else:
                    row[4] = "N/A"

                table_data.append(row)

        table = ax4.table(cellText=table_data, colLabels=headers,
                         cellLoc='center', loc='center')
        table.auto_set_font_size(False)
        table.set_fontsize(10)
        table.scale(1.2, 1.5)
        ax4.set_title('Implementation Comparison Summary', fontsize=12, fontweight='bold')

        plt.tight_layout()
        plt.savefig(self.results_dir / 'comprehensive_summary_analysis.png', dpi=300, bbox_inches='tight')
        plt.close()

        return fig

    def generate_analysis_report(self):
        """Generate comprehensive analysis report"""
        report = []
        report.append("# Comprehensive KNN Implementation Analysis Report")
        report.append("=" * 60)
        report.append("")
        report.append("## Executive Summary")
        report.append("")
        report.append("This report presents a comprehensive analysis of K-Nearest Neighbors (KNN) implementations")
        report.append("across multiple programming languages and frameworks, evaluated on four diverse datasets.")
        report.append("")
        report.append("### Models Analyzed:")
        report.append("- **Python scikit-learn KNN**: Industry-standard implementation")
        report.append("- **Python Manual KNN**: Custom implementation for reference")
        report.append("- **Idris2 Original KNN**: Type-safe functional implementation")
        report.append("- **Idris2 Spidr KNN**: Hardware-accelerated type-safe implementation")
        report.append("- **Reference Implementation**: Baseline comparison")
        report.append("")
        report.append("### Datasets:")
        report.append("- **Iris**: 4 features, 3 classes, 150 samples")
        report.append("- **Wine**: 13 features, 3 classes, 178 samples")
        report.append("- **Breast Cancer**: 30 features, 2 classes, 569 samples")
        report.append("- **Synthetic**: 12 features, 5 classes, 5000 samples")
        report.append("")

        # Accuracy Analysis
        report.append("## Accuracy Analysis")
        report.append("")

        for dataset in self.datasets:
            if any(model in self.data and dataset in self.data[model] and "accuracy" in self.data[model][dataset]
                   for model in self.models):
                report.append(f"### {dataset.replace('_', ' ').title()} Dataset")
                report.append("")

                for model in self.models:
                    if (model in self.data and dataset in self.data[model] and
                        "accuracy" in self.data[model][dataset]):
                        acc = self.data[model][dataset]["accuracy"]
                        model_name = {
                            "python_sklearn": "Python scikit-learn",
                            "python_manual": "Python Manual",
                            "idris": "Idris2 Original",
                            "spidr": "Idris2 Spidr",
                            "reference": "Reference"
                        }.get(model, model)
                        report.append(f"- **{model_name}**: {acc:.2f}%")

                report.append("")

        # Performance Analysis
        report.append("## Performance Analysis")
        report.append("")

        for dataset in self.datasets:
            has_perf_data = any(
                model in self.data and dataset in self.data[model] and
                ("execution_time_ms" in self.data[model][dataset] or "memory_usage_mb" in self.data[model][dataset])
                for model in self.models
            )

            if has_perf_data:
                report.append(f"### {dataset.replace('_', ' ').title()} Dataset Performance")
                report.append("")

                for model in self.models:
                    if model in self.data and dataset in self.data[model]:
                        model_name = {
                            "python_sklearn": "Python scikit-learn",
                            "python_manual": "Python Manual",
                            "idris": "Idris2 Original",
                            "spidr": "Idris2 Spidr",
                            "reference": "Reference"
                        }.get(model, model)

                        perf_info = []
                        if "execution_time_ms" in self.data[model][dataset]:
                            time_ms = self.data[model][dataset]["execution_time_ms"]
                            perf_info.append(f"Execution: {time_ms:.1f}ms")

                        if "memory_usage_mb" in self.data[model][dataset]:
                            mem_mb = self.data[model][dataset]["memory_usage_mb"]
                            perf_info.append(f"Memory: {mem_mb:.1f}MB")

                        if perf_info:
                            report.append(f"- **{model_name}**: {', '.join(perf_info)}")

                report.append("")

        # Key Findings
        report.append("## Key Findings")
        report.append("")

        # Calculate overall statistics
        model_avg_accuracies = {}
        for model in self.models:
            accuracies = []
            for dataset in self.datasets:
                if (model in self.data and dataset in self.data[model] and
                    "accuracy" in self.data[model][dataset]):
                    accuracies.append(self.data[model][dataset]["accuracy"])
            if accuracies:
                model_avg_accuracies[model] = np.mean(accuracies)

        if model_avg_accuracies:
            best_model = max(model_avg_accuracies.items(), key=lambda x: x[1])
            report.append(f"1. **Best Overall Accuracy**: {best_model[0].replace('_', ' ').title()} ({best_model[1]:.2f}%)")

        # Type safety analysis
        report.append("2. **Type Safety**: Idris2 implementations provide compile-time guarantees")
        report.append("   - Dimension checking prevents runtime errors")
        report.append("   - K ≤ n constraint enforced at compile time")

        # Performance insights
        report.append("3. **Performance Characteristics**:")
        report.append("   - Spidr implementation maintains competitive accuracy")
        report.append("   - Type-safe implementations show consistent behavior")
        report.append("   - Hardware acceleration potential with Spidr/XLA")

        report.append("")
        report.append("## Recommendations")
        report.append("")
        report.append("1. **For Production Use**: Python scikit-learn for mature ecosystem")
        report.append("2. **For Type Safety**: Idris2 implementations for critical applications")
        report.append("3. **For Research**: Spidr for exploring type-safe ML with hardware acceleration")
        report.append("4. **For Education**: Manual implementations for understanding algorithms")
        report.append("")

        # Technical Details
        report.append("## Technical Implementation Details")
        report.append("")
        report.append("### Algorithm Parameters")
        report.append("- **K**: 5 neighbors (consistent across all implementations)")
        report.append("- **Distance Metric**: Euclidean distance")
        report.append("- **Tie Breaking**: Deterministic (lowest index/label)")
        report.append("")

        report.append("### Data Preprocessing")
        report.append("- Standardized train/test splits")
        report.append("- Consistent feature scaling where applicable")
        report.append("- Same random seeds for reproducibility")
        report.append("")

        # Save report
        with open(self.results_dir / 'comprehensive_analysis_report.md', 'w') as f:
            f.write('\n'.join(report))

        return '\n'.join(report)

    def run_comprehensive_analysis(self):
        """Run the complete comprehensive analysis"""
        print("Starting Comprehensive KNN Analysis...")
        print("=" * 50)

        # Load all data
        print("1. Loading performance metrics...")
        self.load_performance_metrics()

        print("2. Calculating accuracies...")
        self.calculate_all_accuracies()

        print("3. Measuring execution times...")
        self.measure_execution_times()

        # Generate plots
        print("4. Generating accuracy comparison plot...")
        self.generate_accuracy_comparison_plot()

        print("5. Generating execution time plot...")
        self.generate_execution_time_plot()

        print("6. Generating memory usage plot...")
        self.generate_memory_usage_plot()

        print("7. Generating prediction agreement matrices...")
        self.generate_prediction_agreement_matrix()

        print("8. Generating comprehensive summary plot...")
        self.generate_comprehensive_summary_plot()

        print("9. Generating analysis report...")
        report = self.generate_analysis_report()

        print("=" * 50)
        print("Comprehensive Analysis Complete!")
        print(f"Results saved to: {self.results_dir}")
        print("Generated files:")
        print("- comprehensive_accuracy_comparison.png")
        print("- comprehensive_execution_time_comparison.png")
        print("- comprehensive_memory_usage_comparison.png")
        print("- prediction_agreement_*.png (per dataset)")
        print("- comprehensive_summary_analysis.png")
        print("- comprehensive_analysis_report.md")

        return self.data

if __name__ == "__main__":
    analyzer = ComprehensiveAnalyzer()
    results = analyzer.run_comprehensive_analysis()
