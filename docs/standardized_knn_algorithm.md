# Standardized KNN Algorithm Specification

## Critical Algorithmic Differences Found

### 1. **Sorting Method**
- **KNN.idr**: Uses `sort` (natural ordering)
- **KNN_Configurable.idr**: Uses `sortBy (\(d1, _), (d2, _) => compare d1 d2)`
- **Python**: Uses `np.argsort()` or `heapq.nsmallest()`

### 2. **Majority Vote Implementation**
- **KNN.idr**: Hardcoded for 3 classes with manual counting
- **KNN_Configurable.idr**: Generic approach with `sortBy` on counts
- **Python**: Uses `Counter.most_common(1)[0][0]`

### 3. **Tie-Breaking Strategy**
- **KNN.idr**: First occurrence wins (deterministic)
- **KNN_Configurable.idr**: Depends on `sortBy` stability
- **Python**: Depends on `Counter` implementation

### 4. **Distance Calculation**
- **All implementations**: Use Euclidean distance but with different precision

## Standardized Algorithm

To ensure scientific integrity, all implementations must use **identical algorithms**:

### Distance Calculation
```
euclideanDistance(v1, v2) = sqrt(sum((v1[i] - v2[i])^2 for i in range(features)))
```

### Neighbor Selection
```
1. Calculate distances from query point to all training points
2. Create list of (distance, label) pairs
3. Sort by distance (ascending)
4. Take first k elements
```

### Tie-Breaking for Equal Distances
```
If distances are equal, use training sample index (lower index wins)
```

### Majority Vote
```
1. Extract labels from k nearest neighbors
2. Count occurrences of each label
3. Return label with highest count
4. If counts are tied, return label with lowest numeric value
```

### Deterministic Behavior
```
- Use fixed random seed for any randomization
- Ensure consistent floating-point precision
- Use stable sorting algorithms
- Define explicit tie-breaking rules
```

## Implementation Requirements

### 1. **Identical Distance Calculation**
- Same floating-point precision
- Same mathematical operations order
- Same handling of edge cases

### 2. **Identical Neighbor Selection**
- Same sorting algorithm
- Same tie-breaking for equal distances
- Same k-selection method

### 3. **Identical Majority Vote**
- Same counting method
- Same tie-breaking for equal counts
- Same return value selection

### 4. **Identical Data Handling**
- Same CSV parsing precision
- Same data type conversions
- Same missing value handling

## Validation Requirements

### 1. **Intermediate Result Logging**
- Log distances for first test sample
- Log k nearest neighbors for first test sample
- Log majority vote counts for first test sample

### 2. **Deterministic Testing**
- Use identical test cases across implementations
- Verify intermediate results match exactly
- Test edge cases (ties, equal distances)

### 3. **Cross-Implementation Verification**
- Run same test case on all implementations
- Compare intermediate results step-by-step
- Identify and fix any discrepancies

## Test Cases for Validation

### Test Case 1: Simple 2D Example
```
Training: [(0,0,0), (1,1,1), (2,2,2)]
Query: (0.5, 0.5)
Expected k=2 neighbors: [(0,0,0), (1,1,1)]
Expected prediction: depends on tie-breaking rule
```

### Test Case 2: Equal Distance Tie
```
Training: [(0,0,0), (1,0,1), (0,1,1)]  
Query: (0.5, 0.5)
Expected: All distances equal, use index-based tie-breaking
```

### Test Case 3: Majority Vote Tie
```
k=4 neighbors with labels: [0, 0, 1, 1]
Expected: Use lowest label (0) as tie-breaker
```

## Implementation Status

- [ ] Standardize Idris KNN.idr
- [ ] Standardize Idris KNN_Configurable.idr  
- [ ] Standardize Python manual implementation
- [ ] Standardize Python sklearn wrapper
- [ ] Add comprehensive logging
- [ ] Create validation test suite
- [ ] Verify cross-implementation consistency
