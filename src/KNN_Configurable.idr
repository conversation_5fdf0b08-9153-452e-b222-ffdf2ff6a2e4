module KNN_Configurable

import Data.Vect
import Data.List
import Data.List1
import Data.String
import System.File
import System
import Tensor
import Literal

%ambiguity_depth 5

-- Configurable KNN implementation for different datasets
-- TODO: figure out why this is so slow on large datasets
-- took me forever to get the types right

||| Dataset configuration
record DatasetConfig where
  constructor MkDatasetConfig
  features : Nat
  classes : Nat
  k : Nat
  trainFile : String
  testFile : String
  resultsFile : String

||| Iris dataset configuration
irisConfig : DatasetConfig
irisConfig = MkDatasetConfig 4 3 5 "data/iris_train.csv" "data/iris_test.csv" "results/iris_predictions.txt"

||| Wine dataset configuration
wineConfig : DatasetConfig
wineConfig = MkDatasetConfig 13 3 5 "data/wine_train.csv" "data/wine_test.csv" "results/wine_predictions.txt"

||| Breast cancer dataset configuration
breastCancerConfig : DatasetConfig
breastCancerConfig = MkDatasetConfig 30 2 5 "data/breast_cancer_train.csv" "data/breast_cancer_test.csv" "results/breast_cancer_predictions.txt"

||| Synthetic dataset configuration (5000 samples, 12 features, 4 classes)
syntheticConfig : DatasetConfig
syntheticConfig = MkDatasetConfig 12 4 5 "data/synthetic_train.csv" "data/synthetic_test.csv" "results/synthetic_predictions.txt"



||| Parse a double from string (handles negative numbers)
parseDouble : String -> Maybe Double
parseDouble str =
  case Data.String.parseDouble str of
    Nothing => cast {to=Double} <$> parseInteger str
    Just d => Just d

||| Convert list to vector of known length
listToVect : (n : Nat) -> List a -> Maybe (Vect n a)
listToVect Z [] = Just []
listToVect (S k) (x :: xs) = map (x ::) (listToVect k xs)
listToVect _ _ = Nothing

||| Euclidean distance between two feature vectors
euclideanDistance : {n : Nat} -> Vect n Double -> Vect n Double -> Double
euclideanDistance v1 v2 = sqrt $ sum $ zipWith (\x, y => (x - y) * (x - y)) v1 v2

||| Extract a row from training data matrix
extractRow : {n, m : Nat} -> Fin n -> Vect n (Vect m Double) -> Vect m Double
extractRow i rows = index i rows

||| Find k nearest neighbors with type-safe constraints
findKNearest : {n, m : Nat} -> {k : Nat} -> {auto prf : LTE k n} ->
               Vect m Double ->                    -- query point
               Vect n (Vect m Double) ->           -- training data
               Vect n Nat ->                       -- training labels
               Vect k (Double, Nat)                -- k nearest (distance, label)
findKNearest {n} {k} query trainData trainLabels =
  let indices = Data.Vect.Fin.range {len=n}
      distances = map (\i => let row = extractRow i trainData
                                 dist = euclideanDistance query row
                                 label = index i trainLabels
                             in (dist, label)) indices
      distancesList = toList distances
      sortedDistances = sortBy (\(d1, _), (d2, _) => compare d1 d2) distancesList
      kNearest = take k sortedDistances
  in case listToVect k kNearest of
       Just vect => vect
       Nothing => believe_me ()  -- Should never happen due to type constraints

||| Predict class based on k nearest neighbors (majority vote)
predictClass : {k : Nat} -> Vect k (Double, Nat) -> Nat
predictClass neighbors =
  let labels = map snd neighbors
      labelsList = toList labels
      counts = map (\l => (l, length $ filter (== l) labelsList)) labelsList
      sortedCounts = sortBy (\(_, c1), (_, c2) => compare c2 c1) counts
  in case sortedCounts of
       ((label, _) :: _) => label
       [] => 0  -- this shouldn't happen but just in case

||| Calculate accuracy
accuracy : {n : Nat} -> Vect n Nat -> Vect n Nat -> Double
accuracy predictions actual =
  let correctCount = length $ filter id $ zipWith (==) (toList predictions) (toList actual)
      totalCount = length $ toList predictions
  in cast correctCount / cast totalCount

||| Type-safe KNN classifier
knnClassify : {m, n, f : Nat} -> {k : Nat} -> {auto prf : LTE k n} ->
              Vect m (Vect f Double) ->      -- test data
              Vect n (Vect f Double) ->      -- training data
              Vect n Nat ->                  -- training labels
              Vect m Nat                     -- predictions
knnClassify {k} testData trainData trainLabels =
  map (\queryPoint => let neighbors = findKNearest {k=k} @{prf} queryPoint trainData trainLabels
                      in predictClass neighbors) testData

||| Parse CSV line into features and label (configurable number of features)
parseLineConfig : (features : Nat) -> String -> Maybe (Vect features Double, Nat)
parseLineConfig features line =
  let parts = split (== ',') line
      partsList = forget parts
      expectedParts = S features  -- features + 1 label
  in if length partsList == expectedParts
     then let featureParts = take features partsList
              labelPart = case drop features partsList of
                           [l] => l
                           _ => ""
              parsedFeatures = traverse KNN_Configurable.parseDouble featureParts
              parsedLabel = cast {to=Integer} labelPart
          in case parsedFeatures of
               Just featList => 
                 case listToVect features featList of
                   Just featVect => 
                     if parsedLabel >= 0 
                     then Just (featVect, cast parsedLabel)
                     else Nothing
                   Nothing => Nothing
               Nothing => Nothing
     else Nothing

||| Load data from CSV file (configurable)
loadDataFromCSVConfig : (features : Nat) -> String -> IO (Maybe (n ** (Vect n (Vect features Double), Vect n Nat)))
loadDataFromCSVConfig features filename = do
  Right content <- readFile filename
    | Left err => do putStrLn $ "Error reading file: " ++ show err
                     pure Nothing
  let dataLines = filter (not . null) $ drop 1 $ lines content  -- skip header
  let parsedData = mapMaybe (parseLineConfig features) dataLines
  case parsedData of
    [] => do putStrLn "Error: No valid data parsed"
             pure Nothing
    _ => let featuresData = map fst parsedData
             labels = map snd parsedData
             n = length parsedData
         in case listToVect n featuresData of
              Just featVect => case listToVect n labels of
                Just labelVect => pure $ Just (n ** (featVect, labelVect))
                Nothing => pure Nothing
              Nothing => pure Nothing

||| Run KNN experiment with given configuration
runKNNExperimentConfig : DatasetConfig -> IO ()
runKNNExperimentConfig config = do
  putStrLn $ " Running KNN Experiment: " ++ config.trainFile
  putStrLn "=================================="
  
  putStrLn " Loading training data..."
  Just (trainN ** (trainData, trainLabels)) <- loadDataFromCSVConfig config.features config.trainFile
    | Nothing => do putStrLn " Failed to load training data"
                    pure ()
  
  putStrLn " Loading test data..."  
  Just (testN ** (testData, testLabels)) <- loadDataFromCSVConfig config.features config.testFile
    | Nothing => do putStrLn " Failed to load test data"
                    pure ()
                    
  putStrLn $ " Loaded " ++ show trainN ++ " training samples"
  putStrLn $ " Loaded " ++ show testN ++ " test samples"
  putStrLn $ " Using k = " ++ show config.k ++ " neighbors"
  putStrLn $ " Features: " ++ show config.features ++ ", Classes: " ++ show config.classes
  
  -- Type-safe constraint: k must be ≤ training samples
  case isLTE config.k trainN of
    Yes prf => do
      putStrLn " Running KNN classification..."

      putStrLn " Running KNN classification..."

      -- Perform classification with compile-time guarantees
      let predictions = knnClassify @{prf} testData trainData trainLabels
      let acc = accuracy predictions testLabels

      putStrLn $ " Accuracy: " ++ show (acc * 100) ++ "%"
      
      -- Save predictions
      putStrLn $ " Saving predictions to " ++ config.resultsFile
      let predList = map show $ toList predictions
      let predStr = concat $ intersperse "\n" predList
      Right () <- writeFile config.resultsFile predStr
        | Left err => putStrLn $ "  Warning: Could not save predictions: " ++ show err
      
      putStrLn " KNN experiment completed successfully!"
      
    No _ => do
      putStrLn $ " Error: k=" ++ show config.k ++ " is greater than training samples=" ++ show trainN
      putStrLn "   Please reduce k or increase training data size"

||| Main function - can be configured for different datasets
main : IO ()
main = do
  args <- getArgs
  case args of
    (_ :: "iris" :: _) => runKNNExperimentConfig irisConfig
    (_ :: "wine" :: _) => runKNNExperimentConfig wineConfig
    (_ :: "breast_cancer" :: _) => runKNNExperimentConfig breastCancerConfig
    (_ :: "synthetic" :: _) => runKNNExperimentConfig syntheticConfig
    _ => do
      putStrLn " Configurable KNN Classifier"
      putStrLn "Usage: ./knn_configurable [dataset]"
      putStrLn ""
      putStrLn "Available datasets:"
      putStrLn "  iris         - Iris dataset (4 features, 3 classes, 20 test samples)"
      putStrLn "  wine         - Wine dataset (13 features, 3 classes, 30 test samples)"
      putStrLn "  breast_cancer - Breast cancer dataset (30 features, 2 classes, 100 test samples)"
      putStrLn "  synthetic    - Synthetic dataset (12 features, 4 classes, 1000 test samples)"
      putStrLn ""
      putStrLn "Running default (iris) experiment..."
      runKNNExperimentConfig irisConfig
