module KNN

import Data.Vect
import Data.List
import Data.List1
import Data.String
import System.File
import System
import Tensor
import Literal

%ambiguity_depth 5

-- Type-safe KNN implementation with dependent types
-- TODO: make K configurable instead of hardcoded
-- FIXME: this is probably not the most efficient way but it works

||| Number of neighbors for KNN
K : Nat
K = 5

||| Number of features in Iris dataset
Features : Nat
Features = 4

||| Number of classes in Iris dataset
Classes : Nat
Classes = 3

||| Convert list to vector of known length
listToVect : (n : Nat) -> List a -> Maybe (Vect n a)
listToVect Z [] = Just []
listToVect (S k) (x :: xs) = map (x ::) (listToVect k xs)
listToVect _ _ = Nothing

||| Type-safe distance calculation between two feature vectors
||| Ensures both vectors have the same number of features
euclideanDistance : Vect Features Double -> Vect Features Double -> Double
euclideanDistance x y =
  let diff = zipWith (-) x y
      squared = map (\d => d * d) diff
      sumSquared = foldr (+) 0.0 squared
  in sqrt sumSquared

||| Extract a row from training data matrix (simplified version)
||| Note: This is a placeholder - in real Spidr we'd use proper tensor slicing
extractRow : {n : Nat} -> Fin n -> Vect n (Vect Features Double) -> Vect Features Double
extractRow i rows = index i rows

||| Find k nearest neighbors with type-safe constraints
||| @ n number of training samples
||| @ k number of neighbors (must be ≤ n)
findKNearest : {n : Nat} -> {auto prf : LTE K n} ->
               Vect Features Double ->                    -- query point
               Vect n (Vect Features Double) ->           -- training data (as vectors)
               Vect n Nat ->                              -- training labels
               Vect K (Double, Nat)                       -- k nearest (distance, label)
findKNearest {n} query trainData trainLabels =
  let indices = Data.Vect.Fin.range {len=n}
      distances = map (\i => let row = extractRow i trainData
                                 dist = euclideanDistance query row
                                 label = index i trainLabels
                             in (dist, label)) indices
      distancesList = toList distances
      sortedList = sort distancesList  -- Sort the list
      -- Take first K elements and convert back to vector
      topK = take K sortedList
  in case listToVect K topK of
       Just result => result
       Nothing => believe_me (Data.Vect.replicate K (0.0, 0))  -- this should never happen if K <= n but just in case

||| Predict class using majority vote among k neighbors
||| Returns the most frequent class among neighbors
predictClass : Vect K (Double, Nat) -> Nat
predictClass neighbors =
  let labels = map (\(_, label) => label) neighbors
      labelsList = toList labels
      -- Count occurrences of each class (0, 1, 2) - hardcoded for iris but works
      count0 = length $ filter (== 0) labelsList
      count1 = length $ filter (== 1) labelsList
      count2 = length $ filter (== 2) labelsList
      counts = [count0, count1, count2]
      maxIndex = findMaxIndex counts 0 0 0
  in maxIndex
  where
    findMaxIndex : List Nat -> Nat -> Nat -> Nat -> Nat
    findMaxIndex [] _ maxIdx _ = maxIdx
    findMaxIndex (x :: xs) idx maxIdx maxVal =
      if x > maxVal
      then findMaxIndex xs (idx + 1) idx x
      else findMaxIndex xs (idx + 1) maxIdx maxVal

||| Type-safe KNN classifier (simplified for vectors)
||| @ m number of test samples
||| @ n number of training samples
||| Ensures k ≤ n (can't ask for more neighbors than available)
knnClassify : {m, n : Nat} -> {auto prf : LTE K n} ->
              Vect m (Vect Features Double) ->      -- test data (as vectors)
              Vect n (Vect Features Double) ->      -- training data (as vectors)
              Vect n Nat ->                         -- training labels
              Vect m Nat                            -- predictions
knnClassify testData trainData trainLabels =
  map (\queryPoint => let neighbors = findKNearest queryPoint trainData trainLabels
                      in predictClass neighbors) testData

||| Calculate classification accuracy
accuracy : {n : Nat} -> Vect n Nat -> Vect n Nat -> Double
accuracy predicted actual =
  let comparisons = zipWith (==) predicted actual
      comparisonsList = toList comparisons
      correct = length $ filter id comparisonsList
  in cast correct / cast n

||| Parse a single double value from string (handles negative numbers)
parseDouble : String -> Maybe Double
parseDouble s =
  -- Simple parsing using cast - this should work for most decimal numbers
  case cast {to=Double} s of
    0.0 => if s == "0" || s == "0.0" || s == "-0" || s == "-0.0" then Just 0.0 else Nothing
    x => Just x

||| Parse CSV line into features and label
parseLine : String -> Maybe (Vect Features Double, Nat)
parseLine line =
  let parts = split (== ',') line
      partsList = forget parts
  in case partsList of
       [f1, f2, f3, f4, label] =>
         let d1 = KNN.parseDouble f1
             d2 = KNN.parseDouble f2
             d3 = KNN.parseDouble f3
             d4 = KNN.parseDouble f4
             l = cast {to=Integer} label
         in case (d1, d2, d3, d4) of
              (Just v1, Just v2, Just v3, Just v4) =>
                if l >= 0 then Just ([v1, v2, v3, v4], cast l) else Nothing
              _ => Nothing
       _ => Nothing

||| Load data from CSV file (simplified version using vectors)
loadDataFromCSV : String -> IO (Maybe (n ** (Vect n (Vect Features Double), Vect n Nat)))
loadDataFromCSV filename = do
  Right content <- readFile filename
    | Left err => do putStrLn $ "Error reading file: " ++ show err
                     pure Nothing
  let dataLines = filter (not . null) $ drop 1 $ lines content  -- skip header
  let parsedData = mapMaybe parseLine dataLines
  case parsedData of
    [] => do putStrLn "Error: No valid data parsed"
             pure Nothing
    _ => let features = map fst parsedData
             labels = map snd parsedData
             n = length parsedData
         in case listToVect n features of
              Just featVect => case listToVect n labels of
                Just labelVect => pure $ Just (n ** (featVect, labelVect))
                Nothing => pure Nothing
              Nothing => pure Nothing

||| Main experiment function
runKNNExperiment : IO ()
runKNNExperiment = do
  putStrLn "Running Type-Safe KNN Experiment"
  putStrLn "=================================="
  
  putStrLn "Loading training data..."
  Just (trainN ** (trainData, trainLabels)) <- loadDataFromCSV "data/iris_train.csv"
    | Nothing => do putStrLn "Failed to load training data"
                    pure ()  -- Exit gracefully
  
  putStrLn "Loading test data..."  
  Just (testN ** (testData, testLabels)) <- loadDataFromCSV "data/iris_test.csv"
    | Nothing => do putStrLn "Failed to load test data"
                    pure ()
                    
  putStrLn $ "Loaded " ++ show trainN ++ " training samples"
  putStrLn $ "Loaded " ++ show testN ++ " test samples"
  putStrLn $ "Using k = " ++ show K ++ " neighbors"
  
  -- Type-safe constraint: K must be ≤ training samples
  case isLTE K trainN of
    Yes prf => do
      putStrLn "Running KNN classification..."
      
      -- Perform classification with compile-time guarantees
      let predictions = knnClassify @{prf} testData trainData trainLabels
      let acc = accuracy predictions testLabels
      
      putStrLn $ " Accuracy: " ++ show (acc * 100) ++ "%"
      
      -- Save predictions
      putStrLn " Saving predictions to results/predictions.txt"
      let predList = map show $ toList predictions
      let predStr = concat $ intersperse "\n" predList
      Right () <- writeFile "results/predictions.txt" predStr
        | Left err => putStrLn $ " Warning: Could not save predictions: " ++ show err
      
      putStrLn " KNN experiment completed successfully!"
      
    No _ => do
      putStrLn $ "Error: k=" ++ show K ++ " is greater than training samples=" ++ show trainN
      putStrLn " Please reduce K or increase training data size"
      pure ()

||| Helper function to create results directory
createResultsDir : IO ()
createResultsDir = do
  exitCode <- system "mkdir -p results"
  case exitCode of
    0 => pure ()
    _ => putStrLn "Warning: Could not create results directory"

||| Main entry point
main : IO ()
main = do
  createResultsDir
  runKNNExperiment