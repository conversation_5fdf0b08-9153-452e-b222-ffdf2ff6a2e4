Python KNN Experiment Results
========================================

Configuration:
  k = 5
  distance_metric = euclidean
  weights = uniform

Manual Implementation:
  Accuracy: 93.33%
  Execution time: 0.0271s

Scikit-Learn Implementation:
  Accuracy: 93.33%
  Execution time: 0.0022s

Classification Report (Manual):
              precision    recall  f1-score   support

     Class_0       0.83      1.00      0.91        10
     Class_1       1.00      0.83      0.91        12
     Class_2       1.00      1.00      1.00         8

    accuracy                           0.93        30
   macro avg       0.94      0.94      0.94        30
weighted avg       0.94      0.93      0.93        30


Confusion Matrix (Manual):
[[10  0  0]
 [ 2 10  0]
 [ 0  0  8]]