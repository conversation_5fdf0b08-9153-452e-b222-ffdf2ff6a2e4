Python KNN Experiment Results
========================================

Configuration:
  k = 5
  distance_metric = euclidean
  weights = uniform

Manual Implementation:
  Accuracy: 100.00%
  Execution time: 0.0101s

Scikit-Learn Implementation:
  Accuracy: 100.00%
  Execution time: 0.0026s

Classification Report (Manual):
              precision    recall  f1-score   support

     Class_0       1.00      1.00      1.00         7
     Class_1       1.00      1.00      1.00         7
     Class_2       1.00      1.00      1.00         6

    accuracy                           1.00        20
   macro avg       1.00      1.00      1.00        20
weighted avg       1.00      1.00      1.00        20


Confusion Matrix (Manual):
[[7 0 0]
 [0 7 0]
 [0 0 6]]