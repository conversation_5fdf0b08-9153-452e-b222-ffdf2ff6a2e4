Python KNN Experiment Results
========================================

Configuration:
  k = 5
  distance_metric = euclidean
  weights = uniform

Manual Implementation:
  Accuracy: 94.00%
  Execution time: 0.2576s

Scikit-Learn Implementation:
  Accuracy: 94.00%
  Execution time: 0.0639s

Classification Report (Manual):
              precision    recall  f1-score   support

     Class_0       1.00      0.84      0.91        37
     Class_1       0.91      1.00      0.95        63

    accuracy                           0.94       100
   macro avg       0.96      0.92      0.93       100
weighted avg       0.95      0.94      0.94       100


Confusion Matrix (Manual):
[[31  6]
 [ 0 63]]