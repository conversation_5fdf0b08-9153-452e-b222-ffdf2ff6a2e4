{"validation_timestamp": "2025-07-20T01:20:49+01:00", "validation_start_timestamp": "2025-07-20T01:20:49+01:00", "validation_end_timestamp": "2025-07-20T01:20:49  2025-08-12T01:20:49-08T04:34:27.415343", "datasets": {"breast_cancer": {"dataset_name": "breast_cancer", "files_found": {"breast_cancer_train.csv": true, "breast_cancer_test.csv": true, "breast_cancer_full.csv": true}, "data_integrity": {"X_train_hash": "e6f4c394bdb242b7440c8911becf87e04c08f3550a708672f5503f3fd3d99bab", "y_train_hash": "2620a430a802151533d4b59d9ec9a89215891b9ee99f1f4c0d3e68290a4c03ad", "X_test_hash": "39accf0f0ab42c4153270943b9a63167b1a7fbabae956f5b8e740c80d538bfde", "y_test_hash": "a9408c5f3641d4d9272e6bc312269a182482d48de0dcadbb0bfb7d873bc00450"}, "statistics": {"train_samples": 400, "test_samples": 100, "features": 30, "classes": 2, "train_class_distribution": {"0": 149, "1": 251}, "test_class_distribution": {"0": 37, "1": 63}, "feature_means": [1.000000000139778e-08, 1.000000001250001e-08, -2.500000007010783e-09, 2.500000000349445e-09, 2.0000000005016004e-08, 4.2499999999279225e-08, 3.999999999670934e-08, 1.500000000653756e-08, -2.5000000005714894e-08, 4.2500000009271236e-08, 1.5000000003206892e-08, -3.999999999226844e-08, 1.7500000014658568e-08, -4.4999999990746886e-08, -1.2499999990644994e-08, -2.4999999825858766e-09, -1.750000000466656e-08, 1.0000000007504006e-08, 2.0000000011677342e-08, -2.2500000014247236e-08, -5.551115123125783e-18, 1.2212453270876722e-17, 2.9999999999752445e-08, -2.000000001389779e-08, -2.220446049250313e-18, -2.499999998128999e-09, -6.500000000908557e-08, 1.9999999993913774e-08, 7.50000000326878e-09, -7.50000000659945e-09], "feature_stds": [0.999999991269565, 0.9999999961245124, 0.9999999986024312, 0.9999999939619162, 1.0000000059325647, 1.0000000149744352, 0.9999999910082417, 0.9999999971255749, 1.0000000138858371, 1.000000024417955, 1.0000000023040398, 1.000000022702759, 1.000000014989491, 1.0000000105508489, 0.9999999742110234, 0.9999999905603387, 1.0000000209925286, 0.9999999805843998, 1.0000000425411815, 0.9999999907917211, 0.9999999765107548, 1.0000000138681775, 1.000000010382552, 1.0000000012998322, 1.0000000266655922, 0.9999999668791207, 0.999999975489795, 0.9999999902810998, 0.9999999914845387, 1.0000000120207686], "feature_ranges": {"min": [-1.946213, -2.133119, -1.902471, -1.329425, -3.071532, -1.413645, -1.122648, -1.257818, -2.187303, -1.886442, -0.832007, -1.646268, -0.792665, -0.544928, -1.531888, -1.372264, -1.439965, -1.987906, -1.67391, -1.358505, -1.668034, -2.204848, -1.637536, -1.120838, -1.973432, -1.41186, -1.401602, -1.818231, -2.335052, -1.656365], "max": [3.765232, 4.635713, 3.75469, 4.681483, 4.710314, 3.859882, 4.2032, 3.805236, 3.310689, 5.17207, 6.644262, 4.762037, 6.976098, 7.211915, 6.248539, 4.524539, 4.467897, 4.803152, 5.278738, 7.093381, 3.971432, 3.558058, 4.124426, 5.391283, 4.253232, 4.127439, 4.752833, 2.56145, 6.65106, 5.247881]}}, "issues": []}, "iris": {"dataset_name": "iris", "files_found": {"iris_train.csv": true, "iris_test.csv": true, "iris_full.csv": true}, "data_integrity": {"X_train_hash": "8c36b0c07b1cf09f1a0dc8e2a0bb54a5dec9c6a6aa322d2a504ded64f2155595", "y_train_hash": "4318da3d95354ff1c1fd0d951c60179255be090121a181e82037419f26dd5a67", "X_test_hash": "e99acef202c0f4f8bed14ccafea8f053f7b07a31ad723b276e193673bce603de", "y_test_hash": "b1f18f832a3248650c0f0680466bb918fa234d86765eb1abb9c835fb4d0ff053"}, "statistics": {"train_samples": 80, "test_samples": 20, "features": 4, "classes": 3, "train_class_distribution": {"0": 27, "1": 26, "2": 27}, "test_class_distribution": {"0": 7, "1": 7, "2": 6}, "feature_means": [-8.749999999002612e-08, -1.2500000012849455e-08, -1.2499999990644994e-08, -1.2499999997861445e-07], "feature_stds": [0.9999999814897023, 1.0000000080459686, 1.000000086420565, 0.9999999846268921], "feature_ranges": {"min": [-1.763507, -1.85687, -1.549252, -1.460938], "max": [2.236199, 2.994672, 1.613053, 1.614721]}}, "issues": []}, "synthetic": {"dataset_name": "synthetic", "files_found": {"synthetic_train.csv": true, "synthetic_test.csv": true, "synthetic_full.csv": true}, "data_integrity": {"X_train_hash": "3eb37c0f07ea06481d11e4345d4819d31cb07282b490505b4ab380fa79a443fb", "y_train_hash": "adf83c4eb7f13c5733e1c280a565c38b0159a52459075795f359b029eb8d4da0", "X_test_hash": "94449c49708ebd7b24a3e50a4b1ac438930bf3a7c46b832128826e964d4bde16", "y_test_hash": "295f679424647cd16b447de154ffa3486addbf83f29f753474098ae01a315f13"}, "statistics": {"train_samples": 4000, "test_samples": 1000, "features": 12, "classes": 4, "train_class_distribution": {"0": 995, "1": 998, "2": 1007, "3": 1000}, "test_class_distribution": {"0": 249, "1": 249, "2": 252, "3": 250}, "feature_means": [-4.7500000013300795e-09, 2.6645352591003758e-18, 2.499999922633833e-10, 6.250000002872014e-09, -1.5000000059828268e-09, -3.4999999947160632e-09, 5.249999999623611e-09, 5.7499999996935e-09, -4.999999987376214e-10, 4.750000003106436e-09, -4.999999995813909e-09, 2.5000000007935343e-09], "feature_stds": [0.9999999974490801, 0.9999999999411769, 0.999999996917691, 1.0000000049313458, 1.0000000091484031, 1.000000002347643, 1.0000000047883453, 1.0000000059092962, 1.0000000025127016, 0.9999999988469921, 0.9999999969516805, 0.9999999988830928], "feature_ranges": {"min": [-3.147961, -3.905487, -3.242959, -4.017932, -3.324052, -3.237867, -3.719502, -3.486795, -3.857257, -3.539339, -3.575919, -4.315147], "max": [3.577678, 3.927632, 3.131134, 3.178438, 3.536304, 3.031284, 3.336788, 3.333911, 3.188387, 3.170198, 3.83726, 3.626283]}}, "issues": []}, "wine": {"dataset_name": "wine", "files_found": {"wine_train.csv": true, "wine_test.csv": true, "wine_full.csv": true}, "data_integrity": {"X_train_hash": "6bc8f76250c717fe8c109fd90c8658c3c6a01d37a244ff6499e126cd50dd96ba", "y_train_hash": "8c82e860a2b40d4dc06d22fbd6803b526f2c59553e2da4b0c4b8d8696e0d8aeb", "X_test_hash": "f76dd015fe0dbbcc7744c0dfb32421ed76956df079c3c4b697ed50af17b3f0f6", "y_test_hash": "13bfc3a01c6e8e21ba82957b89fd071a98f70a295b7de999479236a97a83fd2b"}, "statistics": {"train_samples": 120, "test_samples": 30, "features": 13, "classes": 3, "train_class_distribution": {"0": 40, "1": 48, "2": 32}, "test_class_distribution": {"0": 10, "1": 12, "2": 8}, "feature_means": [-4.1666666679892235e-08, 1.66666666689963e-08, 8.33333333449815e-09, -5.833333333223519e-08, -2.5000000021998166e-08, -4.166666666879e-08, -1.666666669120076e-08, 7.50000000326878e-08, -8.333333341899636e-09, 7.500000002528633e-08, -3.333333334909483e-08, -2.583333333398367e-07, 9.166666666837742e-08], "feature_stds": [0.9999999921795782, 1.0000000709748806, 1.0000000766436927, 1.0000000605417922, 1.0000000071166788, 1.0000000135992533, 1.0000000720436222, 1.000000033690059, 0.9999999681276118, 0.9999999823520095, 1.0000000260223072, 1.0000000380321534, 0.9999999373509563], "feature_ranges": {"min": [-2.254741, -1.33754, -3.438443, -2.342416, -1.567569, -1.917806, -1.557351, -1.669392, -2.023838, -1.393256, -2.20173, -1.837845, -1.438897], "max": [1.835481, 2.695715, 3.041666, 3.032258, 3.883762, 2.526655, 2.974079, 2.399751, 2.33095, 3.175136, 1.998502, 1.981359, 2.873063]}}, "issues": []}}, "comparison": {"k_value_compatibility": {"breast_cancer": {"train_samples": 400, "k5_compatible": true}, "iris": {"train_samples": 80, "k5_compatible": true}, "synthetic": {"train_samples": 4000, "k5_compatible": true}, "wine": {"train_samples": 120, "k5_compatible": true}, "min_samples": 80, "k5_valid": true}, "scaling_consistency": {"breast_cancer": {"properly_centered": true, "properly_scaled": true, "max_mean_deviation": 6.500000000908557e-08, "std_range": [0.9999999668791207, 1.0000000425411815]}, "iris": {"properly_centered": true, "properly_scaled": true, "max_mean_deviation": 1.2499999997861445e-07, "std_range": [0.9999999814897023, 1.000000086420565]}, "synthetic": {"properly_centered": true, "properly_scaled": true, "max_mean_deviation": 6.250000002872014e-09, "std_range": [0.999999996917691, 1.0000000091484031]}, "wine": {"properly_centered": true, "properly_scaled": true, "max_mean_deviation": 2.583333333398367e-07, "std_range": [0.9999999373509563, 1.0000000766436927]}}, "size_progression": {"ordered_datasets": [["iris", 80], ["wine", 120], ["breast_cancer", 400], ["synthetic", 4000]], "size_ratios": [{"from": "iris", "to": "wine", "ratio": 1.5}, {"from": "wine", "to": "breast_cancer", "ratio": 3.3333333333333335}, {"from": "breast_cancer", "to": "synthetic", "ratio": 10.0}]}}, "summary": {"total_datasets": 4, "datasets_with_issues": 0, "all_k5_compatible": true, "all_properly_scaled": true}}