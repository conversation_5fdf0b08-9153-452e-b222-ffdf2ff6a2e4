Python KNN Experiment Results
========================================

Configuration:
  k = 5
  distance_metric = euclidean
  weights = uniform

Manual Implementation:
  Accuracy: 88.20%
  Execution time: 26.5084s

Scikit-Learn Implementation:
  Accuracy: 88.30%
  Execution time: 0.0730s

Classification Report (Manual):
              precision    recall  f1-score   support

     Class_0       0.88      0.92      0.90       249
     Class_1       0.92      0.83      0.87       249
     Class_2       0.89      0.87      0.88       252
     Class_3       0.84      0.91      0.87       250

    accuracy                           0.88      1000
   macro avg       0.88      0.88      0.88      1000
weighted avg       0.88      0.88      0.88      1000


Confusion Matrix (Manual):
[[230   3  10   6]
 [  6 206  13  24]
 [ 18   3 219  12]
 [  7  12   4 227]]