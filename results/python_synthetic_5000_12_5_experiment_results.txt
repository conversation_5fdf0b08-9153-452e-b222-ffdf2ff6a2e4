Python KNN Experiment Results
========================================

Configuration:
  k = 5
  distance_metric = euclidean
  weights = uniform

Manual Implementation:
  Accuracy: 83.00%
  Execution time: 52.4391s

Scikit-Learn Implementation:
  Accuracy: 82.80%
  Execution time: 0.1037s

Classification Report (Manual):
              precision    recall  f1-score   support

     Class_0       0.83      0.73      0.77       201
     Class_1       0.85      0.88      0.86       200
     Class_2       0.84      0.81      0.82       200
     Class_3       0.78      0.81      0.79       200
     Class_4       0.85      0.93      0.89       199

    accuracy                           0.83      1000
   macro avg       0.83      0.83      0.83      1000
weighted avg       0.83      0.83      0.83      1000


Confusion Matrix (Manual):
[[146  20  10  20   5]
 [ 17 175   1   5   2]
 [  3   0 161  17  19]
 [ 10   7  14 162   7]
 [  0   3   5   5 186]]