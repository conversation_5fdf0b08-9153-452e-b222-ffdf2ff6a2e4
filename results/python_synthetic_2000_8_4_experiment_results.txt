Python KNN Experiment Results
========================================

Configuration:
  k = 5
  distance_metric = euclidean
  weights = uniform

Manual Implementation:
  Accuracy: 82.50%
  Execution time: 5.8048s

Scikit-Learn Implementation:
  Accuracy: 82.33%
  Execution time: 0.0122s

Classification Report (Manual):
              precision    recall  f1-score   support

     Class_0       0.81      0.75      0.78       150
     Class_1       0.78      0.68      0.73       151
     Class_2       0.77      0.92      0.84       149
     Class_3       0.94      0.95      0.94       150

    accuracy                           0.82       600
   macro avg       0.83      0.83      0.82       600
weighted avg       0.83      0.82      0.82       600


Confusion Matrix (Manual):
[[113  22  12   3]
 [ 23 103  22   3]
 [  2   7 137   3]
 [  1   0   7 142]]