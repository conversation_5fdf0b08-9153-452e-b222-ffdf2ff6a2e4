Python KNN Experiment Results
========================================

Configuration:
  k = 5
  distance_metric = euclidean
  weights = uniform

Manual Implementation:
  Accuracy: 95.61%
  Execution time: 0.5235s

Scikit-Learn Implementation:
  Accuracy: 95.61%
  Execution time: 0.0538s

Classification Report (Manual):
              precision    recall  f1-score   support

     Class_0       0.99      0.89      0.94       127
     Class_1       0.94      1.00      0.97       215

    accuracy                           0.96       342
   macro avg       0.96      0.94      0.95       342
weighted avg       0.96      0.96      0.96       342


Confusion Matrix (Manual):
[[113  14]
 [  1 214]]