{"dataset": "synthetic_2000_8_4", "k": 5, "train_samples": 1400, "test_samples": 600, "features": 8, "classes": 4, "accuracy": 0.8233333333333334, "predictions": [3, 3, 1, 1, 3, 2, 0, 1, 2, 3, 0, 1, 0, 1, 0, 2, 2, 2, 3, 1, 2, 2, 2, 0, 3, 2, 1, 3, 2, 3, 2, 3, 2, 2, 1, 2, 2, 1, 1, 2, 3, 2, 2, 3, 0, 1, 2, 3, 2, 3, 0, 0, 2, 3, 3, 2, 1, 1, 2, 3, 3, 1, 1, 2, 2, 2, 2, 2, 0, 0, 0, 0, 0, 3, 3, 0, 2, 1, 0, 2, 1, 2, 1, 3, 0, 2, 0, 2, 3, 1, 1, 2, 0, 3, 3, 2, 3, 3, 0, 0, 2, 0, 0, 1, 1, 3, 3, 3, 3, 2, 1, 1, 0, 2, 0, 3, 0, 0, 3, 1, 1, 1, 0, 0, 2, 1, 0, 3, 0, 1, 3, 2, 0, 0, 3, 0, 0, 2, 0, 1, 2, 0, 2, 0, 3, 3, 2, 3, 2, 0, 3, 2, 2, 3, 2, 1, 0, 3, 1, 2, 3, 1, 1, 3, 0, 0, 3, 0, 2, 2, 3, 2, 2, 1, 2, 2, 0, 2, 2, 1, 3, 1, 2, 3, 2, 2, 0, 3, 3, 0, 1, 2, 2, 1, 3, 2, 0, 1, 3, 3, 0, 0, 2, 3, 3, 0, 2, 3, 0, 1, 3, 0, 3, 3, 1, 1, 3, 3, 2, 2, 3, 0, 2, 2, 2, 1, 2, 3, 0, 2, 2, 0, 1, 3, 3, 1, 3, 2, 1, 3, 1, 2, 0, 1, 3, 0, 0, 3, 2, 0, 3, 0, 2, 0, 2, 3, 3, 1, 0, 3, 0, 0, 2, 2, 3, 2, 2, 2, 1, 2, 1, 2, 3, 1, 0, 3, 0, 2, 1, 2, 3, 2, 1, 3, 3, 1, 1, 2, 2, 2, 2, 1, 2, 2, 1, 1, 1, 1, 0, 0, 2, 3, 1, 3, 1, 2, 3, 0, 2, 3, 0, 0, 0, 2, 2, 2, 2, 2, 2, 1, 2, 1, 1, 2, 0, 0, 1, 1, 3, 3, 2, 0, 2, 1, 3, 0, 3, 0, 3, 1, 1, 3, 3, 2, 2, 0, 3, 1, 1, 2, 1, 1, 1, 3, 1, 1, 2, 3, 2, 3, 3, 1, 0, 2, 0, 1, 3, 3, 1, 2, 3, 3, 3, 2, 3, 2, 2, 0, 3, 1, 1, 2, 3, 0, 1, 1, 2, 1, 3, 0, 3, 1, 2, 1, 2, 0, 0, 1, 2, 1, 2, 2, 1, 1, 0, 0, 0, 3, 0, 1, 2, 0, 2, 3, 3, 3, 2, 2, 2, 0, 2, 1, 1, 2, 1, 2, 0, 0, 2, 0, 1, 1, 0, 2, 0, 3, 2, 1, 0, 2, 0, 3, 2, 1, 2, 2, 1, 2, 0, 3, 1, 3, 3, 3, 1, 1, 3, 0, 0, 2, 0, 1, 0, 0, 3, 0, 0, 0, 1, 0, 0, 0, 0, 2, 0, 1, 1, 2, 1, 2, 0, 1, 0, 2, 3, 3, 0, 0, 1, 3, 3, 1, 0, 2, 1, 2, 0, 3, 0, 3, 2, 0, 3, 0, 2, 3, 1, 1, 3, 3, 0, 2, 2, 2, 3, 2, 3, 2, 3, 0, 1, 3, 0, 3, 3, 3, 2, 1, 2, 0, 1, 2, 0, 3, 3, 1, 1, 3, 2, 3, 2, 0, 0, 1, 0, 0, 2, 1, 3, 0, 3, 3, 1, 0, 0, 1, 3, 2, 2, 0, 3, 1, 2, 3, 2, 2, 1, 3, 2, 2, 2, 3, 3, 2, 2, 3, 2, 1, 1, 2, 3, 2, 0, 1, 1, 0, 3, 2, 1, 0, 2, 0, 0, 1, 0, 0, 3, 0, 0, 3], "first_query": [-0.345542, -0.75659, 0.139325, 0.592053, 0.774448, -0.353943, 0.354531, 0.608318], "first_neighbors": [[0.7587449490546873, 3], [0.835420840352334, 3], [0.9304118619530816, 3], [0.9534830704763457, 3], [0.9727739586789935, 3]], "first_prediction": 3}