Python KNN Experiment Results
========================================

Configuration:
  k = 5
  distance_metric = euclidean
  weights = uniform

Manual Implementation:
  Accuracy: 91.59%
  Execution time: 0.0590s

Scikit-Learn Implementation:
  Accuracy: 92.52%
  Execution time: 0.0020s

Classification Report (Manual):
              precision    recall  f1-score   support

     Class_0       0.90      1.00      0.95        35
     Class_1       0.97      0.81      0.89        43
     Class_2       0.88      0.97      0.92        29

    accuracy                           0.92       107
   macro avg       0.91      0.93      0.92       107
weighted avg       0.92      0.92      0.91       107


Confusion Matrix (Manual):
[[35  0  0]
 [ 4 35  4]
 [ 0  1 28]]