Python KNN Experiment Results
========================================

Configuration:
  k = 5
  distance_metric = euclidean
  weights = uniform

Manual Implementation:
  Accuracy: 90.70%
  Execution time: 26.9945s

Scikit-Learn Implementation:
  Accuracy: 90.50%
  Execution time: 0.0765s

Classification Report (Manual):
              precision    recall  f1-score   support

     Class_0       0.90      0.91      0.91       802
     Class_1       0.96      0.89      0.92       798
     Class_2       0.97      0.93      0.95       800
     Class_3       0.86      0.88      0.87       798
     Class_4       0.85      0.92      0.88       802

    accuracy                           0.91      4000
   macro avg       0.91      0.91      0.91      4000
weighted avg       0.91      0.91      0.91      4000


Confusion Matrix (Manual):
[[728   9   4  16  45]
 [ 21 711   7  35  24]
 [ 20   1 747  23   9]
 [ 26  15   3 705  49]
 [ 11   4  12  38 737]]