Python KNN Experiment Results
========================================

Configuration:
  k = 5
  distance_metric = euclidean
  weights = uniform

Manual Implementation:
  Accuracy: 94.44%
  Execution time: 0.0465s

Scikit-Learn Implementation:
  Accuracy: 94.44%
  Execution time: 0.0060s

Classification Report (Manual):
              precision    recall  f1-score   support

     Class_0       1.00      1.00      1.00        30
     Class_1       0.90      0.93      0.92        30
     Class_2       0.93      0.90      0.92        30

    accuracy                           0.94        90
   macro avg       0.94      0.94      0.94        90
weighted avg       0.94      0.94      0.94        90


Confusion Matrix (Manual):
[[30  0  0]
 [ 0 28  2]
 [ 0  3 27]]