# KNN Classification with Dependent Types

This project compares K-Nearest Neighbors classification implemented in both Idris2 with Spidr (using dependent types) and Python (standard implementation). The goal is to explore how compile-time type safety and shape checking can benefit machine learning algorithms.

## What This Project Does

The main objectives are to demonstrate type safety through compile-time guarantees for array shapes and algorithm constraints, compare prediction accuracy between type-safe and traditional implementations, measure execution time and accuracy across different approaches, and evaluate what it's like to develop with dependent types versus traditional dynamic typing.

I initially planned to add more datasets beyond Iris, but the current implementation actually works better than expected with larger datasets, so I've focused on making this comparison as thorough as possible.

## Dataset

This project uses the classic Iris flower classification dataset. It has 4 numerical features (sepal length, sepal width, petal length, and petal width), 3 classes representing different species (Setosa, Versicolor, and Virginica), and 150 total samples that are typically split into 105 for training and 45 for testing. The task is multi-class classification using k-nearest neighbors.

Just a heads up - the first run might take a while to process as dependencies are installed and the project is built.

## Project Structure

```
knn-classifier/
├── knn-classifier.ipkg          # Idris package configuration
├── pack.toml                    # Pack dependency management
├── data/                        # Processed datasets
│   ├── iris_train.csv          # Training data (standardized)
│   ├── iris_test.csv           # Test data (standardized)
│   └── iris_full.csv           # Complete dataset
├── src/                         # Idris source files
│   └── KNN.idr                 # Type-safe KNN implementation
├── python/                      # Python implementations
│   ├── data_processing.py      # Dataset preprocessing
│   ├── knn_python.py          # Python KNN (manual + sklearn)
│   └── comparison_plots.py     # Analysis & visualization
├── results/                     # Experiment outputs
├── run_experiment.sh           # Automated experiment runner
└── README.md                   # This file
```

## Installation and Setup

### Prerequisites

You'll need a few things installed before you can run this project:

1. **Idris2 and Pack**: First, install the pack package manager by following the instructions at https://github.com/stefan-hoeck/idris2-pack, then run:
   ```bash
   pack switch latest
   ```

2. **Python Dependencies**: Install the required Python packages:
   ```bash
   pip install numpy pandas scikit-learn matplotlib seaborn
   ```

3. **Spidr Dependencies**: These are installed automatically by the script, but you can also install them manually:
   ```bash
   pack install pjrt-plugin-xla-cpu  # CPU backend
   pack install spidr                # Tensor library
   ```

### Quick Start

The easiest way to run everything is with the automated script:

```bash
chmod +x run_experiment.sh
./run_experiment.sh
```

This script will handle the entire pipeline: checking dependencies, processing the Iris dataset, building the Idris project, running the type-safe KNN classification, running the Python implementations, generating comparison analysis, and creating visualization plots.

## Key Features

### Type-Safe Idris Implementation

The Idris implementation uses dependent types to enforce correctness at compile time. Here's what the key functions look like:

```idris
-- Compile-time shape checking
euclideanDistance : Tensor [Features] F64 -> Tensor [Features] F64 -> F64

-- Constraint: k ≤ n (can't ask for more neighbors than available)
knnClassify : {m, n : Nat} -> {auto prf : LTE K n} ->
              Tensor [m, Features] F64 ->     -- test data
              Tensor [n, Features] F64 ->     -- training data
              Vect n Nat ->                   -- training labels
              Vect m Nat                      -- predictions
```

The type system provides several guarantees that prevent common bugs: vector dimensions must match for distance calculations, the number of neighbors (k) cannot exceed the number of training samples (n), index bounds are automatically checked at compile time, and shape mismatches are caught before the program even runs.

### Python Comparison

For comparison, I've implemented the same algorithm in Python using two approaches: a manual implementation that directly translates the Idris logic for a fair comparison, and a scikit-learn implementation using the optimized library version. The Python versions rely on traditional runtime error handling rather than compile-time guarantees.

## Usage Examples

### Running with Custom Parameters

You can customize the experiment with various options:

```bash
# Use k=3 neighbors instead of default k=5
./run_experiment.sh -k 3

# Use different train/test split
./run_experiment.sh --test-size 0.2

# Clean rebuild everything
./run_experiment.sh --clean

# Skip dependency checking (faster)
./run_experiment.sh --skip-deps
```

### Running Individual Components

If you want to run parts of the experiment separately:

```bash
# Process data only
cd python && python3 data_processing.py

# Build Idris project
pack build knn-classifier

# Run Idris experiment
pack exec knn-classifier

# Run Python experiments
cd python && python3 knn_python.py --k 5

# Generate plots
cd python && python3 comparison_plots.py --all
```

## Results and Analysis

The experiment generates several types of output files in the `results/` directory:

### Prediction Files
- `idris_predictions.txt` - Predictions from the type-safe Idris implementation
- `python_manual_predictions.txt` - Results from the manual Python implementation
- `python_sklearn_predictions.txt` - Results from the scikit-learn implementation

### Visualizations
- `accuracy_comparison.png` - Accuracy comparison across all implementations
- `confusion_matrices.png` - Detailed error analysis for each approach
- `agreement_matrix.png` - Shows how often implementations agree with each other
- `data_visualization.png` - Dataset visualization using PCA

### Reports
- `experiment_summary.md` - A comprehensive summary of all results

## What to Expect

In terms of accuracy, all implementations should achieve similar results (typically over 95% on the Iris dataset since it's a relatively simple classification task).

The real benefits of the type-safe approach become apparent during development. With Idris, you get compile-time detection of shape mismatches, making it impossible to request more neighbors than are available in the training set, automatic verification of index bounds, and algorithm constraints that are encoded directly in the type system.

The development experience differs significantly between the two approaches. With Idris, initial development takes longer as you work with the type system, but you end up with much higher confidence that your code is correct. Python allows for faster prototyping, but you're more likely to encounter runtime errors that could have been caught earlier.

## Troubleshooting

### Common Issues

Here are some problems you might run into and how to fix them:

**Spidr Installation Problems**:
If the PJRT plugin fails to install, try forcing the installation:
```bash
pack install pjrt-plugin-xla-cpu --force
```

**Idris Build Errors**:
Check that spidr is properly installed and that import paths are correct:
```bash
pack query spidr
```

**Missing Python Packages**:
Make sure all required packages are installed:
```bash
# Install all required packages
pip install -r requirements.txt  # if provided
# or manually:
pip install numpy pandas scikit-learn matplotlib seaborn
```

**Permission Errors**:
Make sure the script is executable:
```bash
chmod +x run_experiment.sh
```

### Debug Mode

If you're having trouble figuring out what's going wrong, you can run the script in verbose mode:
```bash
bash -x run_experiment.sh
```

## Extending the Project

### Adding New Algorithms
If you want to implement other machine learning algorithms with the same type-safe approach:
1. Create a new `.idr` file in the `src/` directory
2. Add a corresponding Python implementation for comparison
3. Update the automation script to include your new algorithm
4. Add algorithm-specific visualizations to the analysis

### Using Different Datasets
To test with other datasets:
1. Modify `data_processing.py` to handle your new dataset
2. Update the feature dimensions in the Idris type signatures
3. Adjust the visualization functions to work with different data shapes

### Adding More Metrics
To expand the analysis:
1. Extend the analysis code in `comparison_plots.py`
2. Add new evaluation metrics beyond accuracy
3. Include performance benchmarking to compare execution times

## References

- [Spidr Documentation](https://joelberkeley.github.io/spidr/)
- [Idris2 Tutorial](https://idris2.readthedocs.io/)
- [Pack Package Manager](https://github.com/stefan-hoeck/idris2-pack)
- [Iris Dataset](https://archive.ics.uci.edu/ml/datasets/iris)

## License

This project is released under the MIT License - see the LICENSE file for details.

## Contributing

If you'd like to contribute to this project:
1. Fork the repository
2. Create a feature branch for your changes
3. Make your modifications
4. Add tests if applicable
5. Submit a pull request

---

This project demonstrates how dependent types can be applied to machine learning, comparing traditional runtime-checked implementations with compile-time verified approaches for improved software reliability.