# KNN Classification with Dependent Types

A comparative study of K-Nearest Neighbors classification implemented in both **Idris2 + Spidr** (with dependent types) and **Python** (standard implementation). This project demonstrates the benefits of compile-time type safety and shape checking in machine learning algorithms.

## Project Goals

- **Type Safety**: Demonstrate compile-time guarantees for array shapes and algorithm constraints
- **Correctness**: Compare predictions between type-safe and traditional implementations
- **Performance**: Measure execution time and accuracy across implementations
- **Development Experience**: Evaluate the development process with dependent types

TODO: add more datasets besides iris
UPDATE: actually works better than expected with larger datasets

## Dataset

**Iris Flower Classification**
* Features: 4 numerical features (sepal/petal length & width)
* Classes: 3 species (Setosa, Versicolor, Virginica)
* Samples: 150 total (typically 105 train, 45 test)
* Task: Multi-class classification using k-nearest neighbors

Note: this might take a while to process on first run

## Project Structure

```
knn-classifier/
├── knn-classifier.ipkg          # Idris package configuration
├── pack.toml                    # Pack dependency management
├── data/                        # Processed datasets
│   ├── iris_train.csv          # Training data (standardized)
│   ├── iris_test.csv           # Test data (standardized)
│   └── iris_full.csv           # Complete dataset
├── src/                         # Idris source files
│   └── KNN.idr                 # Type-safe KNN implementation
├── python/                      # Python implementations
│   ├── data_processing.py      # Dataset preprocessing
│   ├── knn_python.py          # Python KNN (manual + sklearn)
│   └── comparison_plots.py     # Analysis & visualization
├── results/                     # Experiment outputs
├── run_experiment.sh           # Automated experiment runner
└── README.md                   # This file
```

## 🔧 Installation & Setup

### Prerequisites

1. **Idris2 + Pack**: Install the pack package manager
   ```bash
   # Follow instructions at: https://github.com/stefan-hoeck/idris2-pack
   pack switch latest
   ```

2. **Python Dependencies**: 
   ```bash
   pip install numpy pandas scikit-learn matplotlib seaborn
   ```

3. **Spidr Dependencies**: Installed automatically by the script
   ```bash
   pack install pjrt-plugin-xla-cpu  # CPU backend
   pack install spidr                # Tensor library
   ```

### Quick Start

Run the complete experiment pipeline:

```bash
chmod +x run_experiment.sh
./run_experiment.sh
```

This will:
1. ✅ Check all dependencies
2. 📂 Process the Iris dataset  
3. 🔨 Build the Idris project
4. 🔍 Run type-safe KNN classification
5. 🐍 Run Python KNN implementations
6. 📊 Generate comparison analysis
7. 📈 Create visualization plots

## 🔍 Key Features

### Type-Safe Idris Implementation

```idris
-- Compile-time shape checking
euclideanDistance : Tensor [Features] F64 -> Tensor [Features] F64 -> F64

-- Constraint: k ≤ n (can't ask for more neighbors than available)
knnClassify : {m, n : Nat} -> {auto prf : LTE K n} ->
              Tensor [m, Features] F64 ->     -- test data
              Tensor [n, Features] F64 ->     -- training data  
              Vect n Nat ->                   -- training labels
              Vect m Nat                      -- predictions
```

**Type Safety Guarantees:**
- ✅ Vector dimensions must match for distance calculations
- ✅ Number of neighbors (k) cannot exceed training samples (n)
- ✅ Index bounds are checked at compile time
- ✅ Shape mismatches caught before runtime

### Python Comparison

- **Manual Implementation**: Direct translation for fair comparison
- **Scikit-Learn**: Optimized library implementation  
- **Runtime Checking**: Traditional error handling approaches

## 🚀 Usage Examples

### Run with Custom Parameters

```bash
# Use k=3 neighbors instead of default k=5
./run_experiment.sh -k 3

# Use different train/test split
./run_experiment.sh --test-size 0.2

# Clean rebuild everything
./run_experiment.sh --clean

# Skip dependency checking (faster)
./run_experiment.sh --skip-deps
```

### Individual Components

```bash
# Process data only
cd python && python3 data_processing.py

# Build Idris project  
pack build knn-classifier

# Run Idris experiment
pack exec knn-classifier

# Run Python experiments
cd python && python3 knn_python.py --k 5

# Generate plots
cd python && python3 comparison_plots.py --all
```

## 📈 Results & Analysis

The experiment generates several outputs in the `results/` directory:

### Prediction Files
- `idris_predictions.txt` - Type-safe Idris predictions
- `python_manual_predictions.txt` - Manual Python implementation  
- `python_sklearn_predictions.txt` - Scikit-learn implementation

### Visualizations
- `accuracy_comparison.png` - Accuracy across implementations
- `confusion_matrices.png` - Detailed error analysis
- `agreement_matrix.png` - Implementation agreement rates
- `data_visualization.png` - Dataset visualization (PCA)

### Reports
- `experiment_summary.md` - Comprehensive results summary

## 🔬 Expected Outcomes

**Accuracy**: All implementations should achieve similar accuracy (>95% on Iris)

**Type Safety Benefits**:
- Compile-time detection of shape mismatches
- Impossible to request more neighbors than available  
- Index bounds automatically verified
- Algorithm constraints encoded in types

**Development Experience**:
- Idris: Longer initial development, but higher confidence
- Python: Faster prototyping, runtime error potential

## 🛠️ Troubleshooting

### Common Issues

**Spidr Installation**: 
```bash
# If PJRT plugin fails, try:
pack install pjrt-plugin-xla-cpu --force
```

**Idris Build Errors**:
```bash
# Check import paths and ensure spidr is installed
pack query spidr
```

**Python Missing Packages**:
```bash
# Install all required packages
pip install -r requirements.txt  # if provided
# or manually:
pip install numpy pandas scikit-learn matplotlib seaborn
```

**Permission Errors**:
```bash
chmod +x run_experiment.sh
```

### Debug Mode

For verbose output and debugging:
```bash
bash -x run_experiment.sh
```

## 🔄 Extending the Project

### Add New Algorithms
1. Create new `.idr` file in `src/`
2. Add corresponding Python implementation
3. Update the automation script
4. Add algorithm-specific visualizations

### Different Datasets
1. Modify `data_processing.py` for new dataset
2. Update feature dimensions in Idris types
3. Adjust visualization functions

### Additional Metrics
1. Extend analysis in `comparison_plots.py`  
2. Add new evaluation metrics
3. Include performance benchmarking

## 📚 References

- [Spidr Documentation](https://joelberkeley.github.io/spidr/)
- [Idris2 Tutorial](https://idris2.readthedocs.io/)
- [Pack Package Manager](https://github.com/stefan-hoeck/idris2-pack)
- [Iris Dataset](https://archive.ics.uci.edu/ml/datasets/iris)

## 📄 License

MIT License - see LICENSE file for details.

## 👥 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes  
4. Add tests if applicable
5. Submit a pull request

---

**🎓 Academic Context**: This project demonstrates the application of dependent types to machine learning, comparing traditional runtime-checked implementations with compile-time verified approaches for improved software reliability.